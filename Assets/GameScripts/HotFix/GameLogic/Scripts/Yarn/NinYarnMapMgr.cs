using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;

public class NinYarnMapMgr {
    private MapLoader _mapLoader;
    private InstanceMapInfo _instanceMapInfo;
    private Dictionary<string, MainServer.ThroughPointInfo> _testThroughPoints = new();
    public NinYarnMapMgr(MapLoader mapLoader) {
        _mapLoader = mapLoader;
    }
    public async UniTask LoadInstancMapInfo(InstanceMapInfo instanceMapInfo) {
        _instanceMapInfo = instanceMapInfo;
        _testThroughPoints.Clear();
        await UpdateThroughPoints();
        // if(instanceMapInfo.trainerQuest != null && instanceMapInfo.trainerQuest.YarnInfo != null && instanceMapInfo.trainerQuest.YarnInfo.ThroughPoints != null) {
        //     // NinYarnMgr.Share.LoadTitle(instanceMapInfo.Name, instanceMapInfo.Name);
        //     // var throughPoints = instanceMapInfo.trainerQuest.YarnInfo.ThroughPoint.Where(item => item.Value).Select(item => item.Key).ToList();
        //     // _mapLoader.UpdateThroughPoints(throughPoints);
        //     UpdateThroughPoints();
        //     // foreach (var pointKv in instanceMapInfo.trainerQuest.YarnInfo.ThroughPoint)
        //     // {
        //     //     if(pointKv.Value) {
        //     //         _mapLoader.UpdateThroughPoints()
        //     //     }
        //     // }
        // }
    }
    private async UniTask UpdateThroughPoints() {
        // if(instanceMapInfo.trainerQuest != null && instanceMapInfo.trainerQuest.YarnInfo != null && instanceMapInfo.trainerQuest.YarnInfo.ThroughPoints != null) {
        // }
        // var throughPoints = instanceMapInfo.trainerQuest.YarnInfo.ThroughPoints.Values.OrderBy(item => item.Index).ToList();
        var throughPoints = _testThroughPoints.Values.OrderBy(item => item.Index).ToList();
        await _mapLoader.UpdateThroughPoints(throughPoints);
    }

    //节点开始，会影响到地图配置
    public async UniTask StartYarnPoint(string pointTitle) {
        //需要同步给后端
        if(_testThroughPoints.ContainsKey(pointTitle)) {
            _testThroughPoints[pointTitle].InfoType = MainServer.ThroughPointInfoType.Start;
        } else {
            _testThroughPoints[pointTitle] = new MainServer.ThroughPointInfo {
                PointTitle = pointTitle,
                InfoType = MainServer.ThroughPointInfoType.Start,
                Index = _testThroughPoints.Count
            };
        }
        UpdateThroughPoints();
    }
    public async UniTask EndYarnPoint(string pointTitle) {
        //需要同步给后端
        if(_testThroughPoints.ContainsKey(pointTitle)) {
            _testThroughPoints[pointTitle].InfoType = MainServer.ThroughPointInfoType.End;
        } else {
            _testThroughPoints[pointTitle] = new MainServer.ThroughPointInfo {
                PointTitle = pointTitle,
                InfoType = MainServer.ThroughPointInfoType.End,
                Index = _testThroughPoints.Count
            };
        }
        UpdateThroughPoints();
    }
}