using System.Collections.Generic;
using UnityEngine;
using Yarn.Unity;
public class NinYarnMgr
{
    // public static NinYarnMgr Share = new NinYarnMgr();
    public Dictionary<string, YarnProject> yarnProjectMap = new();
    public NinYarnMgr(DialogueRunner dialogueRunner) {
        this.dialogueRunner = dialogueRunner;
    }
    public DialogueRunner dialogueRunner { get; private set; }
    public NinCharacter lastTalkCharacter { get; private set; }
    public string lastTalkNameAndTitle { get; private set; }
    public string currentLoadYarnProject { get; private set; }
    public void Talk(NinCharacter target, string yarnName, string yarnTitle) {
        lastTalkCharacter = target;
        var nameAndTitle = yarnName + "_" + yarnTitle;
        if(lastTalkNameAndTitle == nameAndTitle) {
            dialogueRunner.RequestNextLine();
            return;
        }
        lastTalkNameAndTitle = nameAndTitle;
        LoadYarnProjectIfNeed(yarnName);
        dialogueRunner.StartDialogue(yarnTitle);
    }
    public void LoadTitle(string yarnName, string yarnTitle) {
        LoadYarnProjectIfNeed(yarnName);
        if(lastTalkNameAndTitle == yarnTitle) {
            return;
        }
        dialogueRunner.StartDialogue(yarnTitle);
    }
    public void ToCurrentProjectTitle(string yarnTitle) {
        if(string.IsNullOrEmpty(currentLoadYarnProject)) {
            Debug.LogError("请先加载一个yarn project");
            return;
        }
        dialogueRunner.StartDialogue(yarnTitle);
    }
    public void ClearDialogue() {
        lastTalkCharacter = null;
        lastTalkNameAndTitle = null;
        dialogueRunner.Stop();
    }
    public void LoadYarnProjectIfNeed(string yarnName) {
        if(currentLoadYarnProject == yarnName) {
            return;
        }
        if(!yarnProjectMap.TryGetValue(yarnName, out var yarnProject)) {
            yarnProject = AssertResourceLoader.Share.LoadAsset<YarnProject>("Yarn_" + yarnName);
        }
        if(dialogueRunner.IsDialogueRunning) {
            dialogueRunner.Stop();
        }
        dialogueRunner.SetProject(yarnProject);
        currentLoadYarnProject = yarnName;
    }
}