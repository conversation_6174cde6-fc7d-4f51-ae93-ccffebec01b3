using PetsServices.Net;
using UnityEngine;
using Yarn.Unity;
using Cysharp.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Collections;

#nullable enable
public class NinYarnCommand {
    // const string TAG = "NinYarnCommand";
    private static Dictionary<string, NinYarnSelectedItem> _selectedItemMap = new();
    public static NinYarnSelectedItem? GetSelectedItem(string characterName, bool remove = true) {
        if(_selectedItemMap.TryGetValue(characterName, out var item)) {
            if(remove) {
                _selectedItemMap.Remove(characterName);
            }
            return item;
        }
        return null;
    }
    public static bool RemoveSelectedItem(string characterName) {
        return _selectedItemMap.Remove(characterName);
    }

    [YarnCommand("Healing_Pokes")]
    public static void HealingPokes() {
        HealingPokesAsync().Forget();
        // Client.Share.RecoverAroundPokes().AsUniTask().Forget();
    }
    public static async UniTask HealingPokesAsync() {
        await UniTask.Delay(5000);
        Debug.LogError("HealingPokesAsync");
        // Debug.LogError("Fading the camera!");
        // Client.Share.RecoverAroundPokes().AsUniTask().Forget();
    }

    [YarnCommand("Show_Store")]
    public static void ShowStore() {
        Debug.LogError("ShowStore");
        // HealingPokesAsync().Forget();
        // Client.Share.RecoverAroundPokes().AsUniTask().Forget();
    }
    // public static void HeartGoldShip() {
    //     if(NinYarnMgr.Share.lastTalkCharacter == null) {
    //         Debug.LogError("无法获取npc");
    //     }
    //     // yield return HeartGoldShipAsync().ToCoroutine();
    // }
    [YarnCommand("HeartGold_Ship")]
    public static async UniTask HeartGoldShipAsync() {
        Debug.LogError("HeartGoldShip!");
        if(MapController.Current.yarnMgr.lastTalkCharacter == null) {
            Debug.LogError("无法获取npc");
            return;
        }
        var npcConfig = LocalNPC.GetNpcConfig(MapController.Current.yarnMgr.lastTalkCharacter.characterName);
        if(npcConfig == null) {
            Debug.LogError("无法获取npcConfig");
        }
        // NinDialogueOptionUI.Show(new List<string> { "前往1", "前往2", "前往3" }).Forget();
        //显示选项
        var options = new List<string>();
        npcConfig?.DefaultTransferPoints.ToList().ForEach((point) => {
            options.Add(point);
        });
        var item = new NinYarnSelectedItem(NinYarnSelectedItem.SelectedType.InstanceMap);
        var index = await NinDialogueOptionUI.ShowAndWaitSelected(options);
        item.selectedIndex = index;
        item.characterName = MapController.Current.yarnMgr.lastTalkCharacter.characterName;
        if(index == -1) { //cancel
            return;
        }
        item.selectedValue = npcConfig?.DefaultTransferPoints[index];
        _selectedItemMap[item.characterName] = item;
        Debug.LogError("Selected: " + index);
        //传送 --
        // var mapName = npcConfig.DefaultTransferPoints[index];
        // var mapInfo = InstanceMapMgr.GetInstanceMapInfo(mapName);
        // // var mapInfo = MapData.LoadMapData(mapName);
        // if(mapInfo == null) {
        //     Debug.LogError("无法获取mapInfo");
        //     return;
        // }
        // MapController.Current.MoveToNewMap(mapInfo).Forget();
        // mapInfo
        // NinYarnMgr
    }
    [YarnCommand("Start_Transfer")]
    public static void StartTransfer() {
        // Debug.LogError("Transfer_Pokes!");
        // NinYarnMgr
        if(MapController.Current.yarnMgr.lastTalkCharacter == null) {
            Debug.LogError("无法获取npc");
            return;
        }
        var item = NinYarnCommand.GetSelectedItem(MapController.Current.yarnMgr.lastTalkCharacter.characterName);
        if(item != null) {
            if(item.selectedType == NinYarnSelectedItem.SelectedType.InstanceMap && item.selectedIndex >= 0 && !string.IsNullOrEmpty(item.selectedValue)) {
                // MapController.Current.MoveToNewMap(item.selectedValue).Forget();
                var mapName = item.selectedValue;
                var mapInfo = InstanceMapMgr.GetInstanceMapInfo(mapName);
                // var mapInfo = MapData.LoadMapData(mapName);
                if(mapInfo == null) {
                    Debug.LogError("无法获取mapInfo");
                    return;
                }
                MapController.Current.transferMapMgr.MoveToNewMap(MapController.Current.mapCharacterMgr.myCharacter.ninChatacterTransfer, mapInfo).Forget();
            }
        }
    }
    [YarnCommand("Point_Start")]
    public static async UniTask PointStart(string pointTitle) {
        Debug.LogError("PointStart!  " + pointTitle);
        await MapController.Current.yarnMapMgr.StartYarnPoint(pointTitle);
    }

    [YarnCommand("Point_End")]
    public static async UniTask PointEnd(string pointTitle) {
        Debug.LogError("PointEnd!  " + pointTitle);
        await MapController.Current.yarnMapMgr.EndYarnPoint(pointTitle);
    }


    [YarnCommand("Show_Transfer_Point")]
    public static void ShowTransferPoint(string pointTitle) {
        Debug.LogError("ShowTransferPoint!  " + pointTitle);
        // NinYarnMgr
        // if(NinYarnMgr.Share.lastTalkCharacter == null) {
        //     Debug.LogError("无法获取npc");
        //     return;
        // }
        // var item = NinYarnCommand.GetSelectedItem(NinYarnMgr.Share.lastTalkCharacter.characterName);
        // if(item != null) {
        //     if(item.selectedType == NinYarnSelectedItem.SelectedType.InstanceMap && item.selectedIndex >= 0 && !string.IsNullOrEmpty(item.selectedValue)) {
        //         // MapController.Current.MoveToNewMap(item.selectedValue).Forget();
        //         var mapName = item.selectedValue;
        //         var mapInfo = InstanceMapMgr.GetInstanceMapInfo(mapName);
        //         // var mapInfo = MapData.LoadMapData(mapName);
        //         if(mapInfo == null) {
        //             Debug.LogError("无法获取mapInfo");
        //             return;
        //         }
        //         MapController.Current.transferMapMgr.MoveToNewMap(MapController.Current.mapCharacterMgr.myCharacter.ninChatacterTransfer, mapInfo).Forget();
        //     }
        // }
    }
    [YarnCommand("Show_Npc_Point")]
    public static void ShowNpcPoint(string pointTitle) {
        Debug.LogError("ShowNpcPoint!  " + pointTitle);
        
    }

    [YarnCommand("Play_Animation_Point")]
    public static void PlayAnimationPoint(string pointTitle) {
        Debug.LogError("PlayAnimationPoint!  " + pointTitle);
        
        // var fullTitle = NinYarnMgr.Share.currentLoadYarnProject + "_" + pointTitle;
        // NinYarnMgr.Share.ToCurrentProjectTitle(pointTitle);
        
    }
}