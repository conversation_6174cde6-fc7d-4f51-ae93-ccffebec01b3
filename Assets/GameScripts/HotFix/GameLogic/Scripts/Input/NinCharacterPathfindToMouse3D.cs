using System.Collections;
using System.Collections.Generic;
using System.Linq;
using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using PetsServices;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class NinCharacterPathfindToMouse3D : CharacterPathfindToMouse3D
{
    public INinCharacterFollowObject FollowObject;
    public bool IgnoreDetectMouse = false;
    public float FlowDistance = 1f;
    private List<Vector3> _targetPositions = new();
    // private Vector3 _lastFollowPosition = Vector3.zero;
    private System.Int64 _lastLocLineTs;
    private float _lastUpdateTime = 0f;
    private float _updateInterval = 0.1f; // 每 0.1 秒更新一次路径
    public float moveSpeed = 4f; // 插值移动速度
    private bool _isLerping = false;
    private Vector3 _lerpTarget;
    private NinChatacterTransfer _ninChatacterTransfer;
    private Transform _gameObjectTransform {
        get {
            return this.gameObject.transform;
        }
    }
    public Transform Target
    {
        get
        {
            return _characterPathfinder3D.Target;
        }
    }

    public int NextWaypointIndex
    {
        get
        {
            return _characterPathfinder3D.NextWaypointIndex;
        }
    }

    protected override void Initialization()
    {
        base.Initialization();
        _ninChatacterTransfer = this.gameObject.GetComponentInChildren<NinChatacterTransfer>();
        _characterPathfinder3D.OnPathProgress += (int waypointIndex, int waypointsLength, float distance) =>
        {
            if (waypointIndex < 0 && FollowObject == null && !IgnoreDetectMouse)
            {
                VFXPrefabsMgr.Current.GetMapMark().gameObject.SetActive(false);
            }
        };
    }
    public Vector3 LerpingDirection() {
        if(_isLerping) {
            return _lerpTarget - _gameObjectTransform.position;
            // return  _lerpTarget;
        }
        return Vector3.zero;
    }

    public void ClearTargetPositions()
    {
        _targetPositions.Clear();
    }

    public void SetNewTargetPositions(Vector3[] positions, float moveSpeed)
    {
        this.moveSpeed = moveSpeed;
        if(this.moveSpeed < 0.1f)
        {
            this.moveSpeed = 3.5f;
        }
        _targetPositions.AddRange(positions.ToList());
        if (_targetPositions.Count > 0)
        {
            Vector3 nextTarget = _targetPositions[0];
            if (SetNewTargetPosition(nextTarget))
            {
                // _targetPositions.RemoveAt(0);
            }
            // float dist = Vector3.Distance(Destination.transform.position, nextTarget);
            // if (dist > 30f) // 距离非常大时直接瞬移
            // {
            //     transform.position = nextTarget;
            //     Destination.transform.position = nextTarget;
            //     _isLerping = false;
            //     _targetPositions.RemoveAt(0);
            // }
            // else if (dist > 1.5f) // 距离较远才用NavMesh
            // {
            //     SetNewTargetPosition(nextTarget);
            //     _isLerping = false;
            // }
            // else
            // {
            //     _lerpTarget = nextTarget;
            //     _isLerping = true;
            // }
        }
    }
    //
    private bool SetNewTargetPosition(Vector3 position)
    {
        float dist = Vector3.Distance(this.transform.position, position);
        if (dist > 20f) // 距离非常大时直接瞬移
        {
            MapController.Current.transferMapMgr.MoveToCurrentMap(_ninChatacterTransfer, position);
            // _ninChatacterTransfer.Transfer(position, null);
            Destination.transform.position = position;
            _isLerping = false;
            return true;
        }
        else if (dist > 5f) // 距离较远才用NavMesh
        {
            // SetNewTargetPosition(nextTarget);
            Destination.transform.position = position;
            _destinationSet = true;
            // 更新路径目标
            _characterPathfinder3D.SetNewDestination(FollowObject.GetFollowTransform());
            _isLerping = false;
        }
        else
        {
            _lerpTarget = position;
            _isLerping = true;
        }
        return false;
        // float distanceToDestination = Vector3.Distance(Destination.transform.position, position);
        // if (distanceToDestination > 0.03f)
        // {
        //     Destination.transform.position = position;
        //     _destinationSet = true;
        //     // 更新路径目标
        //     _characterPathfinder3D.SetNewDestination(Destination.transform);
        // }
    }

    public override void LateProcessAbility()
    {
        base.LateProcessAbility();
        // 插值平滑移动
        if (_isLerping)
        {
            float dist = Vector3.Distance(_gameObjectTransform.position, _lerpTarget);
            if (dist > 0.05f)
            {
                _gameObjectTransform.position = Vector3.MoveTowards(_gameObjectTransform.position, _lerpTarget, moveSpeed * Time.deltaTime);
                // 动画同步速度
                // var anim = GetComponent<Animator>();
                // if (anim != null)
                // {
                //     anim.SetFloat("Speed", _lerpMoveSpeed);
                // }
            }
            else
            {
                _gameObjectTransform.position = _lerpTarget;
                _isLerping = false;
                if (_targetPositions.Count > 0) _targetPositions.RemoveAt(0);
            }
        }

        long timestamp = System.DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        var time = GameConst.Config.TrainerLocLineTime - 1;
        if (!_isLerping && timestamp - _lastLocLineTs > time && _targetPositions.Count > 0)
        {
            _lastLocLineTs = System.DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            Vector3 targetPosition = _targetPositions.First();
            _targetPositions.Remove(targetPosition);
            SetNewTargetPosition(targetPosition);
        }

        if (FollowObject != null)
        {
            float  distanceToFollowObject = Vector3.Distance(_gameObjectTransform.position, FollowObject.GetFollowTransform().position);
            // float distanceToFollowObject = Vector3.Distance(_lastFollowPosition, FollowObject.transform.position);
            if (distanceToFollowObject > 0.1f)
            {
                if(FollowObject.GetIsOnWater() && _ninChatacterTransfer.ninCharacter.CheckIsOnLandFaceWater()) {
                    MapController.Current.transferMapMgr.MoveToCurrentMap(_ninChatacterTransfer, FollowObject.GetFollowTransform().position);
                } else if(!FollowObject.GetIsOnWater() && _ninChatacterTransfer.ninCharacter.CheckIsOnWaterFaceLand()) {
                    MapController.Current.transferMapMgr.MoveToCurrentMap(_ninChatacterTransfer, FollowObject.GetFollowTransform().position);
                }
                if (distanceToFollowObject <= FlowDistance)
                {
                    StopPath(false);
                    return;
                } else if (distanceToFollowObject > FlowDistance * 3f) {
                    SetNewTargetPosition(FollowObject.GetFollowTransform().position);
                    return;
                }
                // _lastFollowPosition = FollowObject.transform.position;
                _characterPathfinder3D.SetNewDestination(FollowObject.GetFollowTransform());
                OnClickFeedbacks?.PlayFeedbacks(FollowObject.GetFollowTransform().position);
            }
        }
    }

    public virtual void StopPath(bool mapMarkHidenIfNeed = true)
    {
        ClearTargetPositions();
        if(_characterPathfinder3D != null) {
            _characterPathfinder3D.SetNewDestination(_gameObjectTransform);
            if(mapMarkHidenIfNeed) {
                VFXPrefabsMgr.Current.GetMapMark().gameObject.SetActive(false);
            }
        }
    }

    protected override void DetectMouse()
    {
        if (FollowObject != null || IgnoreDetectMouse)
        {
            return;
        }
        if(NinInputContext.mapCanClick == false) {
            return;
        }
        bool testUI = false;

        if (UIShouldBlockInput)
        {
            testUI = PointOrTouchBlockedByUI();
        }

        if (_character != null && _character.LinkedInputManager != null && _character.LinkedInputManager.GetMoveStatus())
        {
            _character.FindAbility<CharacterMovement>().ScriptDrivenInput = false;
            VFXPrefabsMgr.Current.GetMapMark().gameObject.SetActive(false);
            return;
        }
        if (Input.GetMouseButton(MouseButtonIndex) && !testUI)
        {
            if (Time.time - _lastUpdateTime < _updateInterval)
            {
                return;
            }
            _character.FindAbility<CharacterMovement>().ScriptDrivenInput = true;

            Ray ray = _mainCamera.ScreenPointToRay(InputManager.Instance.MousePosition);
            Debug.DrawRay(ray.origin, ray.direction * 100, Color.yellow);

            RaycastHit hit;

            if (Physics.Raycast(ray, out hit))
            {
                Vector3 target = hit.point;
                Destination.transform.position = target;
                _destinationSet = true;

                _characterPathfinder3D.SetNewDestination(Destination.transform);
                VFXPrefabsMgr.Current.GetMapMark().gameObject.SetActive(true);
                VFXPrefabsMgr.Current.GetMapMark().transform.position = new Vector3(target.x, target.y + 0.01f, target.z);
                OnClickFeedbacks?.PlayFeedbacks(Destination.transform.position);
            }
            else
            {
                Debug.Log("Raycast did not hit anything.");
            }
        }
    }

    public bool PointOrTouchBlockedByUI()
    {
        if (EventSystem.current.IsPointerOverGameObject())
        {
            return true;
        }

        if (Input.touchCount > 0)
        {
            if (EventSystem.current.IsPointerOverGameObject(Input.touches[0].fingerId))
            {
                return true;
            }
        }
        return false;
    }

    public static bool IsPointerOverUIObject(Canvas canvas)
    {
        PointerEventData eventData = new PointerEventData(EventSystem.current);
        eventData.position = Input.mousePosition;

        List<RaycastResult> results = new List<RaycastResult>();
        GraphicRaycaster raycaster = canvas.GetComponent<GraphicRaycaster>();
        raycaster.Raycast(eventData, results);
        return results.Count > 0;
    }
}
