using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using MoreMountains.TopDownEngine;
using System;
using PetsServices.Net;
using PetsServices.Store;
using Google.Protobuf;
using System.Linq;
using UnityEngine.AI;
using PokeApiNet;
using System.Threading.Tasks;
using PimDeWitte.UnityMainThreadDispatcher;
using PetsServices;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.CompilerServices;
using Role;
// 角色方向枚举
// public enum CharacterDirection
// {
//     Down,
//     Left,
//     Right,
//     Up
// }

public class NinCharacter : CharacterAbility, INinCharacterFollowObject
{
    public enum NinSpeedType {
        Normal = 14,
        Fast = 6
    }
    public string characterName {  get; private set; }
    public Character TECharacter;
    public GameLogic.UI.Dialogue.NinDialogueZone dialogueZone;
    public NinCharacterPathfindToMouse3D ninCharacterPathfindToMouse3D;
    public NinCharacterCheckCube ninCharacterCheckCube;
    public NinChatacterTransfer ninChatacterTransfer;
    private NinEnvironmentChecker _environmentChecker;
    // public Character gameCreatorCharacter;
    public NinCharacterAnimator animator;
    private Int64 _lastLocLineTs;
    // public info
    public TopDownController3D topDownController3D;
    public bool IsLocalTest = true;
    public MainServer.Trainer trainer { get; private set; }
    private NinCharacter _followCharacter;
    public NinCharacterTopUI ninCharacterTopUI;
    // private Pokemon pokemon;
    private NinNetPokeInfo ninNetPokeInfo;
    private Vector3 _lastUpPosition;
    private CharacterRun _characterRun;
    private NinSpeedType _speedType = NinSpeedType.Normal;
    // public MainServer.RoleDutyType roleDuty = MainServer.RoleDutyType.RoleNormal;
    public MainServer.NpcRoleConfig npcRoleConfig { get; private set; }
    private SyncNinWithGameCreator _syncNinWithGameCreator;
    // private CharacterMovement _characterMovement;
    private NavMeshAgent _navMeshAgent;
    private bool _isOnWater = false;     
    public NinSpeedType speedType {
        get {
            return _speedType;
        }
        set {
            _speedType = value;
            if(_characterRun != null) {
                if(_speedType == NinSpeedType.Fast) {
                    _characterRun.RunStart();
                } else {
                    _characterRun.RunStop();
                }
            }
            if(gameObject) {
                var agent = this.gameObject.GetComponent<NavMeshAgent>();

                if(agent != null) {
                    agent.speed = (float)_speedType;
                }
            }
            ninCharacterPathfindToMouse3D.moveSpeed = (float)_speedType;
        }
    }
    public bool isMe {
        get  {
            return trainer != null && trainer.Id == GameContext.Current.Trainer.Id;
        }
    }
    private bool _canTransfer = false;
    public bool CanTransfer {
        get {
            return this.TECharacter.CharacterType == Character.CharacterTypes.Player && _canTransfer;
        }
        set {
            _canTransfer = value;
        }
    }
    public NinCharacter FollowCharacter
    {
        set
        {
            _followCharacter = value;
            if (ninCharacterPathfindToMouse3D != null)
            {
                ninCharacterPathfindToMouse3D.FollowObject = _followCharacter;
            }
        }
        get
        {
            return _followCharacter;
        }
    }
    private MainServer.TrainerLocInfo _locInfo = new();
    public MainServer.TrainerLocInfo ClearLocInfo() {
        var locInfo = _locInfo;
        //获取navagation的speed
        var agent = this.gameObject.GetComponent<NavMeshAgent>();
        if(agent != null) {
            locInfo.Speed = agent.speed;
        }
        float maxCount = 5f;
        int totalCount = locInfo.LocLine.Count;

        if (totalCount > maxCount)
        {
            List<MainServer.TrainerLoc> selected = new List<MainServer.TrainerLoc>(); // 用实际类型替换 YourType

            for (int i = 0; i < maxCount; i++)
            {
                int index = (int)((i + 1) * totalCount / (maxCount + 1));
                index = Mathf.Clamp(index, 0, totalCount - 1);
                selected.Add(locInfo.LocLine[index]);
            }
            locInfo.LocLine.Clear();
            locInfo.LocLine.AddRange(selected);
        }
        // float maxCount = 5f;
        // if(locInfo.LocLine.Count > maxCount) {
        //     int gapCount = (int)(locInfo.LocLine.Count/maxCount);
        //     int lineCount = locInfo.LocLine.Count;
        //     for (int i = 0; i < lineCount; i++)
        //     {
        //         locInfo.LocLine.RemoveAt(0);
        //     }

        // }
        // PLog.Error("_locInfo.ToByteString().Length: " + _locInfo.ToByteArray().Length);
        _locInfo = new();
        return locInfo;
    }
    public Transform GetFollowTransform() {
        return this.transform;
    }
    public bool GetIsOnWater() {
        return _isOnWater;
    }
    // public void UpdatePathfindTargetLocInfo(MainServer.TrainerLocInfo locInfo) {

    // }
    public bool isMoving
    {
        get
        {
            // var moveStatus = gameCreatorCharacter.LinkedInputManager.GetMoveStatus();
            // if (ninCharacterPathfindToMouse3D != null &&
            // ninCharacterPathfindToMouse3D.Target != null &&
            // gameCreatorCharacter.FindAbility<CharacterMovement>().ScriptDrivenInput == false)
            // {
            //     moveStatus = moveStatus || ninCharacterPathfindToMouse3D.NextWaypointIndex >= 0;
            // }
            // return moveStatus;
            // gameCreatorCharacter.LinkedInputManager.GetMoveStatus() || gameCreatorCharacter.
            if (_characterMovement == null) {
                return false;
            }
            var moveEnabled = (_characterMovement.enabled && _characterMovement.ScriptDrivenInput) || (_navMeshAgent.enabled && _navMeshAgent.enabled);
            //更顺滑
            return moveEnabled && (_character.MovementState.CurrentState == CharacterStates.MovementStates.Walking || _character.MovementState.CurrentState == CharacterStates.MovementStates.Running);
            // return false;
            // float MOVE_THRESHOLD = 1.0f;
            // return gameCreatorCharacter.Driver.WorldMoveDirection.magnitude > 0.2f;
        }
    }
    private MainServer.TrainerActionType _status = MainServer.TrainerActionType.Idle;
    public MainServer.TrainerActionType Status
    {
        get
        {
            return _status;
        }
        set
        {
            _status = value;
        }
    }
    // void Start()
    // {
    //     TrainerResourceInfo info = new();
    //     // if(TECharacter != null && TECharacter.PlayerID == "Player1") {
    //     //     trainer = GameContext.Current.Trainer;
    //     // }

    //     // var trainer = GameContext.Current.Trainer;

    //     info.Name = "01";
    //     info.Gender = "f";
    //     animator.SpriteResourceInfo = info;
    // }
    protected override void PreInitialization()
    {
        base.PreInitialization();
        // if(animator.SpriteResourceInfo == null) {
        //     TrainerResourceInfo info = new();
        //     // if(TECharacter != null && TECharacter.PlayerID == "Player1") {
        //     //     trainer = GameContext.Current.Trainer;
        //     // }

        //     // var trainer = GameContext.Current.Trainer;
        //     if(trainer == null && IsLocalTest) {
        //         info.Name = "01";
        //         info.Gender = "m";
        //     } else {
        //         info.Name = trainer.Cloth.Nbody;
        //         info.Gender = trainer.Gender;
        //     }
        //     animator.SpriteResourceInfo = info;
        // }
        // _characterMovement = this.gameObject.GetComponent<CharacterMovement>();
        _navMeshAgent = this.gameObject.GetComponent<NavMeshAgent>();
        _navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance;
        _characterRun = _character.FindAbility<CharacterRun>();
    }
    void OnDestroy()
    {
        dialogueZone.CloseDialogue();
    }
    protected override void Initialization()
    {
        base.Initialization();
        _syncNinWithGameCreator = this.gameObject.GetComponent<SyncNinWithGameCreator>();
        // Tast().Forget();
        // StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.AllInventory(), (result) =>
        // {
        //     if (result.Success)
        //     {
        //         var jsonResult = JsonFormatter.Default.Format(result.Content);
        //         DefaultKeyValueStore.Instance.Set(GameConst.KVKey.Inventorys, jsonResult);
        //     }
        // }));
    }
    // public async UniTask Tast() {
    //     await UniTask.Delay(1000);
    //     speedType = NinSpeedType.Fast;
    // }
    // private async UniTaskVoid AllInventory()  {
    //     var result = await Client.Share.AllInventory();
    //     if (result.Success) {
    //         var jsonResult = JsonFormatter.Default.Format(result.Content);
    //         DefaultKeyValueStore.Instance.Set(GameConst.KVKey.Inventorys, jsonResult);
    //     }
    // }
    public void OpenCheckCube() {
        ninCharacterCheckCube.gameObject.SetActive(true);
    }
    public bool CheckIsOnWater() {
        _isOnWater = MapController.Current.mapLoader.IsWater(this.transform);
        return _isOnWater;
        // return mapLoader.IsWater(mapCharacterMgr.myCharacter.ninCharacterCheckCube.transform);
    }
    public bool CheckIsOnLandFaceWater() {
        if(_isOnWater) {
            return false;
        }
        return MapController.Current.mapLoader.IsLand(this.ninCharacterCheckCube.transform);
    }
    public bool CheckIsOnWaterFaceLand() {
        if(_isOnWater) {
            return MapController.Current.mapLoader.IsLand(this.ninCharacterCheckCube.transform);
        }
        return false;
    }
    public bool IsOnOutdoorEnv() { //检查是否在室外
        return true;
    }
    public bool TryMoveToNavigableWaterCell() {
        var info = MapController.Current.mapLoader.GetWaterWorldPos(this.ninCharacterCheckCube.transform);
        // info.WordPos.z = info.WordPos.z - 1;
        if(info != null) {
            MapController.Current.transferMapMgr.MoveToCurrentMap(ninChatacterTransfer, info.WordPos);
            // ninChatacterTransfer.Transfer(info.WordPos, (complete) => {

            // });
            return true;
        }
        return false;
    }
    public void MoveToWaterEdgeLand() {
        MapController.Current.transferMapMgr.MoveToCurrentMap(ninChatacterTransfer, this.ninCharacterCheckCube.transform.position);
        // ninChatacterTransfer.Transfer(this.ninCharacterCheckCube.transform.position, (complete) => {

        // });
    }
    public void InitNpc(MainServer.NpcRoleConfig config) {
        npcRoleConfig = config;
        dialogueZone.gameObject.SetActive(true);
        OtherCharacter(config.Name);
        TrainerResourceInfo info = new();
        info.Name = config.Cloth.Name;
        info.Gender = config.Gender;
        animator.SpriteResourceInfo = info;
        ninCharacterTopUI.SetNpc(config);
        // dialogueZone.SetSimpleDialogueElements(config.DialogElements.ToArray());
    }
    public void SetTrainer(MainServer.Trainer trainer, bool forceOther = false)
    {
        dialogueZone.gameObject.SetActive(false);
        this.trainer = trainer;

        ninCharacterTopUI.SetTrainer(trainer);
        if (GameContext.Current.Trainer != null)
        {
            if (forceOther || (this.trainer.Id != GameContext.Current.Trainer.Id && this.TECharacter))
            {
                OtherCharacter(this.trainer.Name);
            }
        }
        TrainerResourceInfo info = new();
        // if(TECharacter != null && TECharacter.PlayerID == "Player1") {
        //     trainer = GameContext.Current.Trainer;
        // }

        // var trainer = GameContext.Current.Trainer;

        info.Name = trainer.Cloth.Name;
        info.Gender = trainer.Gender;
        animator.SpriteResourceInfo = info;
    }
    public void OpenControlCharacter() {
        if (_environmentChecker == null)
        {
            _environmentChecker = this.gameObject.AddComponent<NinEnvironmentChecker>();
        }
        _canTransfer = true;
        OpenCheckCube();
    }
    // public void OpenEnvironmentChecker()
    // {
    // }
    private void OtherCharacter(string name)
    {
        var movement = this.TECharacter.FindAbility<CharacterMovement>();
        movement.Deceleration = 100;
        this.TECharacter.PlayerID = name;
        this.characterName = name;
        this.TECharacter.CharacterType = Character.CharacterTypes.AI;
        this.TECharacter.SetInputManager();
        var characterController = this.TECharacter.gameObject.GetComponent<CharacterController>();
        characterController.radius = 0.1f;
        var center = characterController.center;
        center.y = -90;
        characterController.center = center;
        ninCharacterPathfindToMouse3D.IgnoreDetectMouse = true;
    }
    public void SetPokeInfo(MainServer.TrainerFollowPokeInfo pokeInfo)
    {
        dialogueZone.gameObject.SetActive(false);
        ninCharacterTopUI.SetPokeInfo(pokeInfo);
        if (this.TECharacter != null)
        {
            OtherCharacter(pokeInfo.Id.ToString());
        }
        LoadAnimation(pokeInfo);
    }
    void LoadAnimation(MainServer.TrainerFollowPokeInfo pokeInfo) {
        var localPoke = new NinLocalPokeInfo(pokeInfo.Name);
        // var localPokemonData = await PokeDataLoad.Share.GetIndexData<Pokemon>(pokeInfo.Name);//pokeInfo.LocalPokemonData();
        if(localPoke != null) {
            PokemonResourceInfo resourceInfo = new(pokeInfo.Name, pokeInfo.Shiny, pokeInfo.Gender, PokemonResourceConditions.Followers, localPoke.FormeOrderIndex());
            this.animator.autoRefreshFollowPokeContentSize = true;
            this.animator.infoLoadComplete = (size) => {
                var originScaleToWidth = 64;
                ninCharacterPathfindToMouse3D.FlowDistance = size.x / originScaleToWidth;
            };
            this.animator.SpriteResourceInfo = resourceInfo;
        }
    }
    // void Awake()
    // {
    //     TrainerResourceInfo info = new();
    //     var trainer = GameContext.Current.Trainer;
    //     if(trainer == null && IsLocalTest) {
    //         info.Name = "01";
    //         info.Gender = "m";
    //     } else {
    //         info.Name = trainer.Cloth.Nbody;
    //         info.Gender = trainer.Gender;
    //     }
    //     animator.SpriteResourceInfo = info;
    // }
    // void Start()
    // {
    //     // topDownController3D.MinimumGroundedDistance = Single.MaxValue;
    //     // gameCreatorCharacter.
    //     // GetComponent<CharacterController>().detectCollisions = false;
    //     StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.AllInventory(), (result) => {
    //             if(result.Success) {
    //                 var jsonResult = JsonFormatter.Default.Format(result.Result);
    //                 DefaultKeyValueStore.Instance.Set(GameConst.KVKey.Inventorys, jsonResult);
    //             }
    //     }));
    // }
    public Direction GetCurrentDirection() {
        if(_syncNinWithGameCreator == null) {
            return Role.DirectionHelper.GetFacingDirection(GetMoveDirection()) ?? Direction.N;
        }
        return _syncNinWithGameCreator.currentDirection;
    }
    public void TrySetCurrentDirection(Direction direction) {
        animator.ChangeDirectionIfNeed(direction, true);
    }
    public Vector3 GetMoveDirection() {
        var v3 = ninCharacterPathfindToMouse3D.LerpingDirection();
        if(v3 != Vector3.zero) {
            return v3;
        }
        return topDownController3D.InputMoveDirection;
    }
    public override void ProcessAbility()
    {
        base.ProcessAbility();
        long timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        var time = GameConst.Config.TrainerLocLineTime;
        // if(_lastUpPosition == this.transform.position) {
        //     time = 300 * 10 * 10; // 30s
        // }
        if (timestamp - _lastLocLineTs > time)
        {
            CheckerResult checkerResult = null;
            if (_environmentChecker != null)
            {
                checkerResult = _environmentChecker.DetectGroundEdge();
                if (isMoving)
                {
                    MapController.Current.HandleCheckerResult(this, checkerResult, (success) =>
                    {
                        if (!success)
                        {
                            Status = MainServer.TrainerActionType.Idle;
                        }
                    });
                }
            }
            if(ninCharacterCheckCube.isActiveAndEnabled) {
                ninCharacterCheckCube.UpdateCharacter(GetCurrentDirection());
                if(CheckIsOnWater()) {
                    animator.ChangeStatusIfNeed(NinCharacterStatus.Surf);
                    if(CheckIsOnWaterFaceLand()) {
                         ninCharacterTopUI.oprationTip.ConfigType(NinChatacterOprationTip.OprationType.OnWaterFaceLand);
                    } else {
                        ninCharacterTopUI.oprationTip.ConfigType(NinChatacterOprationTip.OprationType.Water);
                    }
                    // ninCharacterTopUI.oprationTip.ConfigType(NinChatacterOprationTip.OprationType.Water);
                    // Debug.LogError($"角色{this.trainer.Name}在水中");
                } else {
                    animator.ChangeStatusIfNeed(NinCharacterStatus.Normal);
                    if(CheckIsOnLandFaceWater()) {
                        ninCharacterTopUI.oprationTip.ConfigType(NinChatacterOprationTip.OprationType.OnLandFaceWater);
                        // TryMoveToNavigableWaterCell();
                        // Debug.LogError($"角色{this.trainer.Name}在水边上，并且面向着水面");
                    } else {
                        ninCharacterTopUI.oprationTip.ConfigType(NinChatacterOprationTip.OprationType.None);
                    }
                }
            }
            _lastLocLineTs = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            if(checkerResult != null && checkerResult.tilePrefabComponent != null) {
                var loc = new MainServer.TrainerLoc
                {
                    ReginId = checkerResult.tilePrefabComponent.RegionId,
                    X = transform.position.x,
                    Y = transform.position.y,
                    Z = transform.position.z,
                };
                if(!loc.Equals(_locInfo.LocLine.LastOrDefault())) {
                    _locInfo.LocLine.Add(loc);
                    // 限制路径点数量，避免数据包过大
                    // if(_locInfo.LocLine.Count > 5) {
                    //     _locInfo.LocLine.RemoveAt(0);
                    // }
                }
                _locInfo.Loc = new MainServer.TrainerLoc
                {
                    ReginId = checkerResult.tilePrefabComponent.RegionId,
                    X = transform.position.x,
                    Y = transform.position.y,
                    Z = transform.position.z
                };
            }
        }
        //不能注释，不然topdown的状态一致都不是落地的状态
        topDownController3D.Grounded = true;
        // _locInfo.Loc = new MainServer.TrainerLoc
        // {
        //     ReginId = "test_map",
        //     X = transform.position.x,
        //     Y = transform.position.y,
        //     Z = transform.position.z
        // };

        // if(pokemon != null) {
        //     PokemonResourceInfo info = new();
        //     info.Conditions = PokemonResourceConditions.Followers;
        //     info.Pokemon = pokemon;
        //     info.Shiny = false;
        //     info.Gender = "";
        //     this.animator.SpriteResourceInfo = info;
        //     pokemon = null;
        // }
        
        // // if(TECharacter != null && TECharacter.PlayerID == "Player1") {
        // //     trainer = GameContext.Current.Trainer;
        // // }
        // if (_environmentChecker != null)
        // {
        //     if (isMoving)
        //     {
        //         var result = _environmentChecker.DetectGroundEdge();
        //         MapController.Current.HandleCheckerResult(this, result, (success) =>
        //         {
        //             if (!success)
        //             {
        //                 Status = MainServer.TrainerActionType.Idle;
        //             }
        //         });
        //     }
        // }

        // if(_character.CharacterType == Character.CharacterTypes.Player && trainer != null && Status != MainServer.TrainerActionType.Battle) {
        //     // if(_environmentChecker != null) {
        //     //     if(isMoving && _environmentChecker.DetectGroundEdge()) {
        //     //         MapController.current.EnterWildBattle();
        //     //     }
        //     // }
        //     UpdateLoc();
        // }
    }
    // public void StopMove() {
    //     _character.FindAbility<CharacterMovement>().ScriptDrivenInput = false;
    //     // _character.MovementState.ChangeState(CharacterStates.MovementStates.Idle);
    //     // _character.Freeze();
    //     // _character.UnFreeze();
    // }
    public void Freeze() {
        _character.Freeze();
    }
    public void UnFreeze() {
        _character.UnFreeze();
    }
    // void Update()
    // {
    //     topDownController3D.Grounded = true;
    //     if(_character.CharacterType == Character.CharacterTypes.Player && trainer != null && Status != MainServer.TrainerActionType.Battle) { 
    //         UpdateLoc();
    //     }
    // }

    // void UpdateLoc() {
    //     long timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
    //     var time = 300;
    //     if(_lastUpPosition == this.transform.position) {
    //         time = 300 * 10 * 10; // 30s
    //     }
    //     if(timestamp - lastLocTs > time) {
    //         Debug.Log($"=========:1 {this.transform.position}");
    //         StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.UpdateUserLoc("test_map", this.transform.position.x, this.transform.position.y, this.transform.position.z), (result) => {

    //             if(result.Success) {
    //                 _lastUpPosition = this.transform.position;
    //                 // Debug.Log($"=========:2 {result.Result.Trainers.FirstOrDefault().Loc}");
    //                 MapController.Current.UpdateOtherTrainers(result.Result.Trainers.ToArray());
    //             }
    //         }));
    //         lastLocTs = timestamp;
    //     }
    //     if(lastLocTs == 0) {
    //         lastLocTs = timestamp;
    //     }
    // }

}