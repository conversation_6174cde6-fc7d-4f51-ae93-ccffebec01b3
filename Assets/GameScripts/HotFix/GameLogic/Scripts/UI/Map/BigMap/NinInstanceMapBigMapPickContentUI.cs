using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;
public class NinInstanceMapBigMapPickContentUI: MonoBehaviour {
    // public TMP_Text titleText;
    public TMP_Text countText;
    public TMP_Text timeText;
    public Button commitBtn;
    private InstanceMapInfo _instanceMapInfo;
    public Action<InstanceMapInfo> commitAction;
    void Awake() {
        commitBtn.onClick.AddListener(async () => {
            //获取任务的信息
            _instanceMapInfo.trainerQuest = null;
            commitAction?.Invoke(_instanceMapInfo);
        });
    }
    public void ConfigContent(NinBigMapPickUI.NinBigMapListItem item) {
        _instanceMapInfo = item.mapInfo as InstanceMapInfo;
        countText.text = item.displayName;
        
    }
}