using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
public class NinBigMapPickUI : MonoBehaviour
{
    public class NinBigMapListItem {
        public MainServer.MainLandType mainLandType;
        public string displayName;
        public string prefabName;
        public IMapInfo mapInfo;
    }
    public Transform mapContent;
    public NinInstanceMapBigMapPickContentUI instanceMapContent;
    // public ScrollRect mapScrollContent;
    public TableListView tableListView;
    public Button cancelButton;
    public TMP_Text tipText;
    public Action<NinBigMapPoint> selectedAction;
    List<NinBigMapListItem> _mapList;
    private int _selectedIndex;
    private bool _oldMapCanClick;
    void Awake()
    {
        cancelButton.onClick.AddListener(() =>
        {
            Hide();
        });
    }
    public void Show()
    {
        this.gameObject.SetActive(true);
        var mapList = GetDefaultMapList();
        mapList.AddRange(GetInstanceMapItems());
        //判断是否要添加副本地图
        ConfigMapList(mapList);
        _oldMapCanClick = NinInputContext.mapCanClick;
        NinInputContext.mapCanClick = false;
    }
    public List<NinBigMapListItem> GetInstanceMapItems() {
        var mapList = new List<NinBigMapListItem>();
        var mapInfos = InstanceMapMgr.Share.GetItemActiveInstanceMapInfos();
        foreach (var item in mapInfos)
        {
            
            mapList.Add(new NinBigMapListItem {
                displayName = item.NameLocalized,
                mainLandType = MainServer.MainLandType.MainLandNone,
                mapInfo = item
            });
        }
        // if(GameContext.Current.Trainer.Items[]) {

        // }
        // {
        //     new NinBigMapListItem {
        //         displayName = "心金魂银",
        //         prefabName = "BigHeartGlodMap",
        //         mainLandType = MainServer.MainLandType.MainLandHeartGold
        //     }
        // };
        return mapList;
    }
    public void Hide()
    {
        this.gameObject.SetActive(false);
         NinInputContext.mapCanClick = _oldMapCanClick;
    }

    public void ConfigMapList(List<NinBigMapListItem> mapList)
    {
        _mapList = mapList;
        // foreach (Transform child in layoutGroup.transform)
        // {
        //     Destroy(child.gameObject);
        // }
        // foreach (var option in options)
        // {
        //     var prefabCellObject = GameObject.Instantiate(cellPrefab, layoutGroup.transform);
        //     prefabCellObject.gameObject.SetActive(true);
        //     var cell = prefabCellObject.GetComponent<NinDialogueOptionCell>();
        //     cell.ConfigCell(option);
        // }
        tableListView.allCount = () => {
            return _mapList.Count;
        };
        tableListView.indexCellAction = async (index, cts, callback) => {
            // await UniTask.Delay(1000, cancellationToken: cts);
            if(index >= _mapList.Count) {
                callback(null);
                return;
            }
            var option = _mapList[index];
            
            var icon = await DefaultResourceInfo.GetIconMapSprite(option.mapInfo.GetMapNameId(), cts);
            callback(new TableListViewCellItem {
                index = index,
                title = option.displayName,
                icon = icon
            });
        };
        tableListView.selectedAction = async (item) => {
            _selectedIndex = item.index;
            await ShowDetailMap(_mapList[_selectedIndex]);
            //mapContent 去load 地图
            // var selectedItem = filterPokeModels[item.index];
            // selectedAction?.Invoke(selectedItem);
            Debug.Log("Selected: " + item.title);
        };
        tableListView.reloadData();
    }
    private async UniTask ShowDetailMap(NinBigMapListItem bigMapItem) {
        if(bigMapItem.mainLandType == MainServer.MainLandType.MainLandNone || string.IsNullOrEmpty(bigMapItem.prefabName)) {
            instanceMapContent.gameObject.SetActive(true);
            mapContent.gameObject.SetActive(false);
            instanceMapContent.ConfigContent(bigMapItem);
            instanceMapContent.commitAction = async (instanceMapInfo) => {

                await MapController.Current.transferMapMgr.MoveToNewMap(MapController.Current.mapCharacterMgr.myCharacter.ninChatacterTransfer, instanceMapInfo);
                MapController.Current.mapMenuUI.bigMapPickUI.Hide();
                // NinYarnMgr.Share.LoadTitle(instanceMapInfo.Name, instanceMapInfo.Name);
            };
            // IconTitleAlertView.Alert("是否进入副本地图", null, async (success) =>{
            //     // Debug.Log("进入副本:");
            //     if(success) {
            //         await MapController.Current.transferMapMgr.MoveToNewMap(MapController.Current.mapCharacterMgr.myCharacter.ninChatacterTransfer, bigMapItem.mapInfo);
            //         MapController.Current.mapMenuUI.bigMapPickUI.Hide();
            //     }
            // });
        } else {
            instanceMapContent.gameObject.SetActive(false);
            mapContent.gameObject.SetActive(true);
            foreach(Transform item in mapContent) {
                Destroy(item.gameObject);
            }
            GameObject prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(bigMapItem.prefabName);
            if(prefab != null) {
                var objs = await GameObject.InstantiateAsync(prefab, mapContent).ToUniTask();
                var rectTransform = objs[0].GetComponent<RectTransform>();
                rectTransform.anchoredPosition = Vector2.zero;
                // objs[0].transform.setan
                // GameObject transmitObj = objs[0];//
            }
        }
    }
    private List<NinBigMapListItem> GetDefaultMapList() {
        var mapList = new List<NinBigMapListItem> {
            new NinBigMapListItem {
                displayName = "心金魂银",
                prefabName = "BigHeartGlodMap",
                mainLandType = MainServer.MainLandType.MainLandHeartGold,
                mapInfo = MainMapInfo.Create(MainServer.MainLandType.MainLandHeartGold)
            }
        };
        return mapList;
    }
    // public static async UniTask<NinDialogueOptionUI> Show(List<string> options)
    // {
    //     var window = await GameModule.UI.ShowUIAsyncAwait<NinDialogueOptionWindow>();
    //     window.ui.ConfigOptions(options);
    //     return window.ui;
    // }
    // public static async UniTask<int> ShowAndWaitSelected(List<string> options) {
    //     MapController.Current.mapCharacterMgr.FreezeMyCharacter();
    //     var ui = await Show(options);
    //     ui.selectedIndex = -1;
    //     while(ui.selectedIndex == -1) {
    //         await UniTask.Delay(100);
    //     }
    //     GameModule.UI.CloseUI<NinDialogueOptionWindow>();
    //     MapController.Current.mapCharacterMgr.UnFreezeMyCharacter();
    //     return ui.selectedIndex;
    // }
}
        