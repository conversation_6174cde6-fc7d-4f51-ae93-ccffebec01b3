#if UNITY_EDITOR
using System.Collections.Generic;
using UnityEditor;
#endif
using UnityEngine;

// [ExecuteAlways]
public class NinMapNPCComponent : MapYarnOprationComponent
{
    public string npcConfigName; // NPC名称
    public bool defaultIsHiden = false;
    // public bool active; // 是否激活
    // public bool male; // 是否是男性
    // public NinCharacter ninCharacter;
    public NinCharacterAnimator animator;


    // public string yarnTitle;
    // public MapYarnOprationType yarnStartOprationType;
    // public MapYarnOprationType yarnEndOprationType;
    // public List<string> yarnTitleList; //如果需要多个就用这个设置 每一个title都有对应开始和结束
    // public List<MapYarnOprationType> yarnStartOprationTypeList;//如果需要多个就用这个设置
    // public List<MapYarnOprationType> yarnEndOprationTypeList;
    // public List<string> yarnStartOprationValues;//对应操作的值，比如要转成什么状态
    // public List<string> yarnEndOprationValues;

    void Awake()
    {
        // ninCharacter = GetComponent<NinCharacter>();
        animator = GetComponent<NinCharacterAnimator>();
        if(!string.IsNullOrEmpty(npcConfigName)) {
            var npcConfig = LocalNPC.GetNpcConfig(npcConfigName);
            if(npcConfig != null) {
                LoadNPCConfig(npcConfig);
            }
        }
    }
    // void OnValidate()
    // {
    //     // 属性发生变化时触发（比如你在 Inspector 改了 Material）
    //     UpdateMaterial();
    // }
    void LoadNPCConfig(MainServer.NpcRoleConfig npcRoleConfig)
    {
        TrainerResourceInfo info = new();
        // if(TECharacter != null && TECharacter.PlayerID == "Player1") {
        //     trainer = GameContext.Current.Trainer;
        // }

        // var trainer = GameContext.Current.Trainer;

        info.Name = npcRoleConfig.Name;
        info.Gender = npcRoleConfig.Gender;
        animator.SpriteResourceInfo = info;
        // Debug.Log("发生了修改");
        //     if (_renderer != null && newMaterial != null)
        //     {
        //         _renderer.sharedMaterial = newMaterial;

        // #if UNITY_EDITOR
        //         SceneView.RepaintAll(); // 强制刷新 Scene 视图
        // #endif
        //     }
    }
}