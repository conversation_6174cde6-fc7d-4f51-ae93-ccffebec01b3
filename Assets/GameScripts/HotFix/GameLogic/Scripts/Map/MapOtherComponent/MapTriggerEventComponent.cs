using UnityEngine;
public class MapTriggerEventComponent : MonoBehaviour
{
    public string eventName;
    public string eventValue;
    public MapTriggerEventType triggerEventType;
    private void OnTriggerEnter(Collider other)
    {
        Debug.Log($"OnTriggerEnter: MapTriggerEventComponent");
    }

    private void OnTriggerExit(Collider other)
    {
        Debug.Log($"OnTriggerExit: MapTriggerEventComponent");
    }
}
public enum MapTriggerEventType {
    None,
    Yarn,
    Weather
}