using System.Collections.Generic;
using UnityEngine;
public class MapYarnOprationComponent : MonoBehaviour
{
    public string yarnTitle;
    public MapYarnOprationType yarnStartOprationType;
    public MapYarnOprationType yarnEndOprationType;
    public List<string> yarnTitleList; //如果需要多个就用这个设置 每一个title都有对应开始和结束
    public List<MapYarnOprationType> yarnStartOprationTypeList;//如果需要多个就用这个设置
    public List<MapYarnOprationType> yarnEndOprationTypeList;
    public List<string> yarnStartOprationValues;//对应操作的值，比如要转成什么状态
    public List<string> yarnEndOprationValues;
}

public enum MapYarnOprationType {
    None,
    Hide,
    Show,
    ChangeStatus
}
