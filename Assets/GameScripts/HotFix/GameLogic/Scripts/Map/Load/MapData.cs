// ====== 数据结构 ======
using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEngine;

[System.Serializable]
public class PrefabData
{
    public string name;
    public Vector3 position;
    public Vector3 rotation;
    public Vector3 scale;
    public string prefab;
    public PrefabTransmitMapInfo transmitMapInfo;
    public Dictionary<string, PrefabMapTileInfo> mapTileInfos;
}

[System.Serializable]
public class PrefabTransmitMapInfo
{
    public string name;
    public string toMapName;
    public Vector3 position;
    public Vector3 localScale;
    public Vector3 boxColliderSize;
    public bool isHidenEffect;
    public bool isPokeCenter;
}

[System.Serializable]
public class MapTriggerEventComponentInfo
{
    public string eventName;
    public string eventValue;
    public MapTriggerEventType triggerEventType;
    public Vector3 position;
    public Vector3 localScale;
    public Vector3 boxColliderSize;

}

[System.Serializable]
public class PrefabMapTileInfo {
    public string Bgm;
    public string RegionId;
    public string AreaId;
    public string BattleBgm;
    public bool IsBattle = false;
    public MapTileType TileType = MapTileType.Grass;
}

[System.Serializable]
public class WaterMapData
{
    public int gridSize;
    public float cellSize;
    public List<WaterMapBlock> blocks;
}

[System.Serializable]
public class WaterMapBlock
{
    public string blockName;
    public Vector3 localPosition;
    public WaterCell[] waterCells;
}

[System.Serializable]
public class WaterCell
{
    public Vector2Int coord;
    public float surfaceY;
    public bool isWaterEdge;
}
// [System.Serializable]
// public class NPCExportData
// {
//     public string name;
//     public Vector3 position;
// }
// [System.Serializable]
// private class NPCDataList
// {
//     public List<NPCData> data;
// }

[System.Serializable]
public class MapNPCData
{
    public string name;
    public Vector3 position;
    public bool defaultIsHiden;
    public List<MapYarnOprationData> oprationDatas;
}
[System.Serializable]
public class MapRuntimeObstacleData
{
    public string name;
    public Vector3 position;
    public Vector3 scale;
    public bool defaultIsHiden;
    public List<MapYarnOprationData> oprationDatas;
}

[System.Serializable]
public class MapYarnOprationData
{
    // public List<string> yarnTitleList; //如果需要多个就用这个设置
    // public List<MapYarnOprationType> yarnOprationTypeList;//如果需要多个就用这个设置
    // public List<string> yarnOprationValues;//对应操作的值，比如要转成什么状态
    // public List<string> yarnTitleList; //如果需要多个就用这个设置 每一个title都有对应开始和结束
    // public List<MapYarnOprationType> yarnStartOprationTypeList;//如果需要多个就用这个设置
    // public List<MapYarnOprationType> yarnEndOprationTypeList;
    // public List<string> yarnStartOprationValues;//对应操作的值，比如要转成什么状态
    // public List<string> yarnEndOprationValues;
    public string yarnTitle;
    public MapYarnOprationType yarnStartOprationType;
    public MapYarnOprationType yarnEndOprationType;
    public string yarnStartOprationValue;
    public string yarnEndOprationValue;
}

[System.Serializable]
public class MapInitPostion
{
    // public string gen;
    public string tagName;
    public Vector3 position;
    public PrefabTransmitMapInfo transmitMapInfo;
}

[System.Serializable]
public class MapData
{
    public List<PrefabData> mainMap;
    public List<PrefabData> buildingMap;
    public List<PrefabData> transferMap;
    public List<PrefabData> envMap;
    public List<PrefabData> envOutBuildingMap;
    public List<PrefabData> multipleOutMap;
    public List<PrefabData> interiorMap;
    public List<PrefabData> otherMap;
    // public Dictionary<string, List<PrefabData>> prefabData;
    public WaterMapData waterMap;
    public List<MapNPCData> npcs;
    public List<MapInitPostion> initPostions;
    public List<MapRuntimeObstacleData> runtimeObstacles;
    public List<MapTriggerEventComponentInfo> triggerEventComponents;
    public static MapData LoadMapData(string mapJsonName)
    {
        var json = AssertResourceLoader.Share.LoadAsset<TextAsset>(mapJsonName).text;
        var settings = new JsonSerializerSettings
        {
            Converters = new[] {
                new Vec3Conv(),
                //new StringEnumConverter(),
            },
        };
        var mapData = JsonConvert.DeserializeObject<MapData>(json, settings);
        return mapData;
    }
}