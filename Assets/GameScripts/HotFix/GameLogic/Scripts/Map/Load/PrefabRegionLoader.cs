using System.Collections.Generic;
using UnityEngine;
using System.IO;
using Cysharp.Threading.Tasks;

/// <summary>
/// 区域预制体加载器，支持三维空间哈希，动态加载/卸载场景中的物体（包括传送点）
/// </summary>
public class PrefabRegionLoader
{

    // 所有预制体数据的原始坐标映射（Vector3 -> 数据）
    private Dictionary<Vector3, PrefabData> _prefabMap = new();

    // 三维空间哈希字典，key 为空间格子（cell），value 是该格子内的 prefab 列表
    private Dictionary<Vector3Int, List<PrefabItem>> _spatialHashMap = new();

    // 已加载的位置集合
    private HashSet<Vector3> _loadedPositions = new();

    // 当前加载在场景中的实例列表
    private List<LoadedItem> _loadedInstances = new();

    // 缓存的附近格子中的所有 prefab 列表（避免重复查找）
    private List<PrefabItem> _cachedNearbyPrefabs = new();

    // 地图挂载容器（通常是空 GameObject）
    private Transform _mapContainer;

    private string _regionName;
    private Vector3? _lastCenter = null;

    // 加载半径，单位是世界坐标（米）
    public float loadRadius = 70f;

    // 是否打印调试信息
    public bool debugLog = false;

    // 每个空间格子的大小（立方体边长），单位：米
    private readonly int _cellSize = 32;

    // 卸载半径，设为加载半径的 1.5 倍
    private float unloadRadius => loadRadius * 1.5f;

    // 所有传送点信息（根据 prefab 数据附带的结构）
    private Dictionary<string, PrefabTransmitMapInfo> _transmitPoints = new();

    // 当前场景中已生成的传送点对象（按名称索引）
    private Dictionary<string, GameObject> _loadedTransmitPoints = new();

    // 是否正在异步加载中
    private bool _isloading = false;
    private IMapInfo _mapInfo;
    /// <summary>
    /// 构造函数，初始化区域加载器
    /// </summary>
    public PrefabRegionLoader(Transform mapContainer, IMapInfo mapInfo)
    {
        _mapInfo = mapInfo;
        // regionName = "HeartGold"; // 示例强制赋值，可改为参数传入
        // _regionName = regionName;
        _mapContainer = mapContainer;
        // _configFileNames = configFileNames;
        InitData();
    }

    /// <summary>
    /// 加载给定位置附近的对象（支持动态卸载远处对象）
    /// </summary>
    public void LoadNearby(Vector3 worldPosition)
    {
        AsyncLoadNearby(worldPosition).Forget();
    }

    /// <summary>
    /// 异步加载附近 prefab 的主逻辑函数
    /// </summary>
    public async UniTask AsyncLoadNearby(Vector3 worldPosition)
    {
        if (_isloading) return;

        if (_mapContainer == null)
        {
            Debug.LogError("Map container is not set.");
            return;
        }

        _isloading = true;

        // 转换为 local 坐标
        Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);

        // 如果第一次加载，或距离上次加载中心超出范围，就重新构建 nearby 缓存
        if (!_lastCenter.HasValue || Vector3.Distance(localPosition, _lastCenter.Value) > loadRadius)
        {
            _lastCenter = localPosition;
            _cachedNearbyPrefabs.Clear();

            int searchRadius = Mathf.CeilToInt((loadRadius * 2) / _cellSize);

            // Y 方向的搜索限制层数：只搜索上下共3层（-1, 0, +1）
            int ySearchLimit = 1;

            Vector3Int centerCell = WorldToCell(localPosition);

            for (int dx = -searchRadius; dx <= searchRadius; dx++)
            {
                for (int dy = -ySearchLimit; dy <= ySearchLimit; dy++)
                {
                    for (int dz = -searchRadius; dz <= searchRadius; dz++)
                    {
                        Vector3Int cell = new Vector3Int(centerCell.x + dx, centerCell.y + dy, centerCell.z + dz);

                        if (_spatialHashMap.TryGetValue(cell, out var list))
                            _cachedNearbyPrefabs.AddRange(list);
                    }
                }
            }
        }
        
        var cachedNearbyCopy = new List<PrefabItem>(_cachedNearbyPrefabs);

        foreach (var item in cachedNearbyCopy)
        {
            float dist = Vector3.Distance(localPosition, item.position);

            if (dist <= loadRadius && !_loadedPositions.Contains(item.position))
            {
                // 如果是传送点
                if (!string.IsNullOrEmpty(item.transmitName) && _transmitPoints.TryGetValue(item.transmitName, out var transmitInfo))
                {
                    GameObject prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(item.prefabName);
                    if (prefab != null)
                    {
                        var objs = await GameObject.InstantiateAsync(prefab, _mapContainer).ToUniTask();
                        GameObject transmitObj = objs[0];//GameObject.Instantiate(prefab, _mapContainer);
                        transmitObj.transform.SetParent(_mapContainer);
                        transmitObj.transform.localPosition = transmitInfo.position;
                        transmitObj.transform.localRotation = Quaternion.identity;

                        var transmitComponent = transmitObj.GetComponent<TransmitMapComponent>();
                        transmitComponent.Name = transmitInfo.name;
                        transmitComponent.ToMapName = transmitInfo.toMapName;
                        transmitComponent.prefabRegionLoader = this;
                        transmitComponent.transform.localScale = transmitInfo.localScale;
                        transmitComponent.isHidenEffect = transmitInfo.isHidenEffect;
                        transmitComponent.isPointCenter = transmitInfo.isPokeCenter;

                        var collider = transmitObj.GetComponent<BoxCollider>();
                        collider.size = transmitInfo.boxColliderSize;
                        collider.isTrigger = true;

                        _loadedTransmitPoints[transmitInfo.name] = transmitObj;
                        _loadedPositions.Add(item.position);
                        _loadedInstances.Add(new LoadedItem { instance = transmitObj, position = item.position });

                        if (debugLog)
                            Debug.Log($"✅ Created transmit point: {transmitInfo.name} at {transmitInfo.position}");
                    }
                }
                else // 普通 prefab
                {
                    GameObject prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(item.prefabName);
                    if (prefab != null)
                    {
                        var objs = await GameObject.InstantiateAsync(prefab, _mapContainer).ToUniTask();
                        GameObject obj = objs[0];//GameObject.Instantiate(prefab, _mapContainer);
                        obj.name = prefab.name;
                        var key = _mapContainer.name + "_" + obj.name;
                        if(item.mapTileInfos != null && item.mapTileInfos.TryGetValue(key, out var objMapTileInfo))
                        {
                            var mapTileComponent = obj.gameObject.GetComponent<MapTilePrefabComponent>();
                            if(mapTileComponent != null)
                            {
                                mapTileComponent.Init(objMapTileInfo);
                            } else {
                                obj.gameObject.AddComponent<MapTilePrefabComponent>().Init(objMapTileInfo);
                            }
                        }
                        foreach (Transform child in obj.transform)
                        {
                            key = obj.name + "_" + child.gameObject.name;
                            if(item.mapTileInfos != null && item.mapTileInfos.TryGetValue(key, out var mapTileInfo))
                            {
                                var mapTileComponent = child.gameObject.GetComponent<MapTilePrefabComponent>();
                                if(mapTileComponent != null)
                                {
                                    mapTileComponent.Init(mapTileInfo);
                                } else {
                                    child.gameObject.AddComponent<MapTilePrefabComponent>().Init(mapTileInfo);
                                }
                            }
                        }
                        // foreach (Transform item in obj.transform)
                        // {
                            
                        // }
                        obj.transform.SetParent(_mapContainer);
                        obj.transform.localPosition = item.position;
                        obj.transform.localRotation = Quaternion.Euler(item.rotation);
                        obj.transform.localScale = item.scale;
                        obj.name = item.displayName;

                        _loadedInstances.Add(new LoadedItem { instance = obj, position = item.position });
                        _loadedPositions.Add(item.position);

                        if (debugLog)
                            Debug.Log($"✅ Loaded: {item.prefabName} at {item.position}");
                    }
                }
            }
        }

        UnloadFar(localPosition);
        _isloading = false;
    }

    /// <summary>
    /// 卸载离当前位置太远的 prefab 实例
    /// </summary>
    private void UnloadFar(Vector3 localPos)
    {
        for (int i = _loadedInstances.Count - 1; i >= 0; i--)
        {
            var item = _loadedInstances[i];
            if (Vector3.Distance(localPos, item.position) > unloadRadius)
            {
                if (_loadedTransmitPoints.ContainsValue(item.instance))
                {
                    var transmitComponent = item.instance.GetComponent<TransmitMapComponent>();
                    if (transmitComponent != null)
                    {
                        _loadedTransmitPoints.Remove(transmitComponent.Name);
                    }
                }

                GameObject.Destroy(item.instance);
                _loadedPositions.Remove(item.position);
                _loadedInstances.RemoveAt(i);

                if (debugLog)
                    Debug.Log($"♻️ Unloaded object at {item.position}");
            }
        }
    }

    /// <summary>
    /// 初始化数据：解析 JSON 配置、构建哈希表
    /// </summary>
    private void InitData()
    {
        _prefabMap.Clear();
        _transmitPoints.Clear();
        List<PrefabData> prefabData = new();
        var mapData = _mapInfo.GetMapData();
        if(mapData == null) {
            return;
        }
        if(mapData.mainMap != null) {
            prefabData.AddRange(mapData.mainMap);
        }
        if(mapData.buildingMap != null) {
            prefabData.AddRange(mapData.buildingMap);
        }
        if(mapData.transferMap != null) {
            prefabData.AddRange(mapData.transferMap);
        }
        if(mapData.envMap != null) {
            prefabData.AddRange(mapData.envMap);
        }
        if(mapData.envOutBuildingMap != null) {
            prefabData.AddRange(mapData.envOutBuildingMap);
        }
        if(mapData.multipleOutMap != null) {
            prefabData.AddRange(mapData.multipleOutMap);
        }
        if(mapData.interiorMap != null) {
            prefabData.AddRange(mapData.interiorMap);
        }
        if(mapData.otherMap != null) {
            prefabData.AddRange(mapData.otherMap);
        }
        foreach (var item in prefabData)
        {
            Vector3 pos = item.position;
            _prefabMap[pos] = item;

            if (item.transmitMapInfo != null && !string.IsNullOrEmpty(item.transmitMapInfo.name))
            {
                _transmitPoints[item.transmitMapInfo.name] = item.transmitMapInfo;
            }
        }
        foreach (var initPostion in mapData.initPostions)
        {
            _transmitPoints[initPostion.transmitMapInfo.name] = initPostion.transmitMapInfo;
        }
        // foreach (var configFileName in _configFileNames)
        // {
        //     var json = AssertResourceLoader.Share.LoadAsset<TextAsset>(configFileName).text;
        //     var list = JsonUtility.FromJson<PrefabDataList>(json);

        //     foreach (var item in list.data)
        //     {
        //         Vector3 pos = item.position;
        //         _prefabMap[pos] = item;

        //         if (item.transmitMapInfo != null && !string.IsNullOrEmpty(item.transmitMapInfo.name))
        //         {
        //             _transmitPoints[item.transmitMapInfo.name] = item.transmitMapInfo;
        //         }
        //     }
        // }

        BuildSpatialHash();
    }

    /// <summary>
    /// 构建三维空间哈希映射
    /// </summary>
    private void BuildSpatialHash()
    {
        _spatialHashMap.Clear();

        foreach (var kv in _prefabMap)
        {
            Vector3 pos = kv.Key;
            Vector3Int cell = WorldToCell(pos);

            if (!_spatialHashMap.TryGetValue(cell, out var list))
            {
                list = new List<PrefabItem>();
                _spatialHashMap[cell] = list;
            }

            list.Add(new PrefabItem
            {
                position = pos,
                rotation = kv.Value.rotation,
                scale = kv.Value.scale,
                prefabName = kv.Value.prefab,
                displayName = kv.Value.name,
                transmitName = kv.Value.transmitMapInfo?.name ?? "",
                mapTileInfos = kv.Value.mapTileInfos
            });
        }
    }

    /// <summary>
    /// 将世界坐标转为空间格子坐标（三维）
    /// </summary>
    private Vector3Int WorldToCell(Vector3 pos)
    {
        return new Vector3Int(
            Mathf.FloorToInt(pos.x / _cellSize),
            Mathf.FloorToInt(pos.y / _cellSize),
            Mathf.FloorToInt(pos.z / _cellSize)
        );
    }

    public PrefabTransmitMapInfo GetTransmitPointInfo(string transmitPointName)
    {
        _transmitPoints.TryGetValue(transmitPointName, out var info);
        return info;
    }

    public GameObject GetLoadedTransmitPoint(string transmitPointName)
    {
        _loadedTransmitPoints.TryGetValue(transmitPointName, out var obj);
        return obj;
    }

    private class PrefabItem
    {
        public Vector3 position;
        public Vector3 rotation;
        public Vector3 scale;
        public string prefabName;
        public string displayName;
        public string transmitName;
        public Dictionary<string, PrefabMapTileInfo> mapTileInfos;
    }
// public class PrefabData
// {
//     public string name;
//     public Vector3 position;
//     public Vector3 rotation;
//     public Vector3 scale;
//     public string prefab;
//     public PrefabTransmitMapInfo transmitMapInfo;
//     public Dictionary<string, PrefabMapTileInfo> mapTileInfos;
// }
    private class LoadedItem
    {
        public GameObject instance;
        public Vector3 position;
    }
}
