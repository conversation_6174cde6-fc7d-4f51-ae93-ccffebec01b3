using UnityEngine;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using System.Linq;

/// <summary>
/// 地图事件对象加载器，负责动态加载运行时障碍物和触发事件组件
/// 参考NPCLoader和PrefabRegionLoader的模式实现
/// </summary>
public class MapEventObjectLoader
{
    private Transform mapContainer;
    private float loadRadius = 50f;
    private float unloadRadius => loadRadius * 1.5f;

    // 运行时障碍物相关
    private List<MapRuntimeObstacleData> _allRuntimeObstacles = new();
    private Dictionary<string, LoadedRuntimeObstacle> _loadedObstacles = new();
    private HashSet<Vector3> _loadedObstaclePositions = new();

    // 触发事件组件相关
    private List<MapTriggerEventComponentInfo> _allTriggerEvents = new();
    private Dictionary<string, LoadedTriggerEvent> _loadedTriggerEvents = new();
    private HashSet<Vector3> _loadedTriggerPositions = new();

    private Vector3? _lastCenter = null;
    private bool _isLoading = false;

    /// <summary>
    /// 已加载的运行时障碍物信息
    /// </summary>
    private class LoadedRuntimeObstacle
    {
        public GameObject Instance;
        public Vector3 Position;
        public MapRuntimeObstacleData Data;
    }

    /// <summary>
    /// 已加载的触发事件信息
    /// </summary>
    private class LoadedTriggerEvent
    {
        public GameObject Instance;
        public Vector3 Position;
        public MapTriggerEventComponentInfo Data;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="mapContainer">地图容器Transform</param>
    /// <param name="runtimeObstacles">运行时障碍物数据列表</param>
    /// <param name="triggerEvents">触发事件组件数据列表</param>
    public MapEventObjectLoader(Transform mapContainer,
        List<MapRuntimeObstacleData> runtimeObstacles,
        List<MapTriggerEventComponentInfo> triggerEvents)
    {
        this.mapContainer = mapContainer;
        _allRuntimeObstacles = runtimeObstacles ?? new List<MapRuntimeObstacleData>();
        _allTriggerEvents = triggerEvents ?? new List<MapTriggerEventComponentInfo>();

        Debug.Log($"MapEventObjectLoader初始化: {_allRuntimeObstacles.Count}个运行时障碍物, {_allTriggerEvents.Count}个触发事件");
    }

    /// <summary>
    /// 设置加载半径
    /// </summary>
    public void SetLoadRadius(float radius)
    {
        loadRadius = radius;
    }

    /// <summary>
    /// 根据位置动态加载附近的事件对象
    /// </summary>
    public void LoadNearbyEventObjects(Vector3 worldPosition)
    {
        LoadNearbyEventObjectsAsync(worldPosition).Forget();
    }

    /// <summary>
    /// 异步加载附近事件对象的主逻辑
    /// </summary>
    public async UniTask LoadNearbyEventObjectsAsync(Vector3 worldPosition)
    {
        if (_isLoading) return;
        if (mapContainer == null)
        {
            Debug.LogError("MapEventObjectLoader: Map container is not set.");
            return;
        }

        _isLoading = true;

        // 转换为本地坐标
        Vector3 localPosition = mapContainer.InverseTransformPoint(worldPosition);

        // 检查是否需要更新（避免频繁加载）
        if (_lastCenter.HasValue && Vector3.Distance(_lastCenter.Value, localPosition) < loadRadius * 0.3f)
        {
            _isLoading = false;
            return;
        }

        _lastCenter = localPosition;

        // 卸载远距离的对象
        await UnloadDistantObjects(localPosition);

        // 加载运行时障碍物
        await LoadNearbyRuntimeObstacles(localPosition);

        // 加载触发事件组件
        await LoadNearbyTriggerEvents(localPosition);

        _isLoading = false;
    }

    /// <summary>
    /// 卸载远距离的对象
    /// </summary>
    private async UniTask UnloadDistantObjects(Vector3 centerPosition)
    {
        // 卸载远距离的运行时障碍物
        var obstaclesToUnload = _loadedObstacles.Where(kvp =>
            Vector3.Distance(kvp.Value.Position, centerPosition) > unloadRadius).ToList();

        foreach (var obstacle in obstaclesToUnload)
        {
            if (obstacle.Value.Instance != null)
            {
                GameObject.Destroy(obstacle.Value.Instance);
            }
            _loadedObstacles.Remove(obstacle.Key);
            _loadedObstaclePositions.Remove(obstacle.Value.Position);
        }

        // 卸载远距离的触发事件
        var eventsToUnload = _loadedTriggerEvents.Where(kvp =>
            Vector3.Distance(kvp.Value.Position, centerPosition) > unloadRadius).ToList();

        foreach (var triggerEvent in eventsToUnload)
        {
            if (triggerEvent.Value.Instance != null)
            {
                GameObject.Destroy(triggerEvent.Value.Instance);
            }
            _loadedTriggerEvents.Remove(triggerEvent.Key);
            _loadedTriggerPositions.Remove(triggerEvent.Value.Position);
        }

        if (obstaclesToUnload.Count > 0 || eventsToUnload.Count > 0)
        {
            Debug.Log($"卸载了 {obstaclesToUnload.Count} 个障碍物和 {eventsToUnload.Count} 个触发事件");
        }

        await UniTask.Yield();
    }

    /// <summary>
    /// 加载附近的运行时障碍物
    /// </summary>
    private async UniTask LoadNearbyRuntimeObstacles(Vector3 centerPosition)
    {
        foreach (var obstacleData in _allRuntimeObstacles)
        {
            float distance = Vector3.Distance(obstacleData.position, centerPosition);

            // 检查是否在加载范围内且未加载
            if (distance <= loadRadius && !_loadedObstaclePositions.Contains(obstacleData.position))
            {
                await CreateRuntimeObstacle(obstacleData);
            }
        }
    }

    /// <summary>
    /// 创建运行时障碍物
    /// </summary>
    private async UniTask CreateRuntimeObstacle(MapRuntimeObstacleData obstacleData)
    {
        // 创建空的GameObject作为障碍物
        GameObject obstacleObj = new GameObject(obstacleData.name);
        obstacleObj.transform.SetParent(mapContainer, false);
        obstacleObj.transform.localPosition = obstacleData.position;
        obstacleObj.transform.localScale = obstacleData.scale;

        // 添加MapRuntimeObstacle组件
        var obstacleComponent = obstacleObj.AddComponent<MapRuntimeObstacle>();
        obstacleComponent.defaultIsHiden = obstacleData.defaultIsHiden;

        // 设置Yarn操作数据
        if (obstacleData.oprationDatas != null && obstacleData.oprationDatas.Count > 0)
        {
            var firstOpData = obstacleData.oprationDatas[0];
            obstacleComponent.yarnTitle = firstOpData.yarnTitle;
            obstacleComponent.yarnStartOprationType = firstOpData.yarnStartOprationType;
            obstacleComponent.yarnEndOprationType = firstOpData.yarnEndOprationType;

            // 如果有多个操作数据，设置到列表中
            if (obstacleData.oprationDatas.Count > 1)
            {
                obstacleComponent.yarnTitleList = new List<string>();
                obstacleComponent.yarnStartOprationTypeList = new List<MapYarnOprationType>();
                obstacleComponent.yarnEndOprationTypeList = new List<MapYarnOprationType>();
                obstacleComponent.yarnStartOprationValues = new List<string>();
                obstacleComponent.yarnEndOprationValues = new List<string>();

                foreach (var opData in obstacleData.oprationDatas)
                {
                    obstacleComponent.yarnTitleList.Add(opData.yarnTitle);
                    obstacleComponent.yarnStartOprationTypeList.Add(opData.yarnStartOprationType);
                    obstacleComponent.yarnEndOprationTypeList.Add(opData.yarnEndOprationType);
                    obstacleComponent.yarnStartOprationValues.Add(opData.yarnStartOprationValue);
                    obstacleComponent.yarnEndOprationValues.Add(opData.yarnEndOprationValue);
                }
            }
        }

        // 根据defaultIsHiden设置初始状态
        obstacleObj.SetActive(!obstacleData.defaultIsHiden);

        // 记录已加载的障碍物
        string key = $"{obstacleData.name}_{obstacleData.position}";
        _loadedObstacles[key] = new LoadedRuntimeObstacle
        {
            Instance = obstacleObj,
            Position = obstacleData.position,
            Data = obstacleData
        };
        _loadedObstaclePositions.Add(obstacleData.position);

        await UniTask.Yield();
    }

    /// <summary>
    /// 加载附近的触发事件组件
    /// </summary>
    private async UniTask LoadNearbyTriggerEvents(Vector3 centerPosition)
    {
        foreach (var eventData in _allTriggerEvents)
        {
            float distance = Vector3.Distance(eventData.position, centerPosition);

            // 检查是否在加载范围内且未加载
            if (distance <= loadRadius && !_loadedTriggerPositions.Contains(eventData.position))
            {
                await CreateTriggerEvent(eventData);
            }
        }
    }

    /// <summary>
    /// 创建触发事件组件
    /// </summary>
    private async UniTask CreateTriggerEvent(MapTriggerEventComponentInfo eventData)
    {
        // 创建空的GameObject作为触发事件
        GameObject eventObj = new GameObject($"TriggerEvent_{eventData.eventName}");
        eventObj.transform.SetParent(mapContainer, false);
        eventObj.transform.localPosition = eventData.position;
        eventObj.transform.localScale = eventData.localScale;

        // 添加BoxCollider作为触发器
        var boxCollider = eventObj.AddComponent<BoxCollider>();
        boxCollider.isTrigger = true;
        boxCollider.size = eventData.boxColliderSize;

        // 添加MapTriggerEventComponent组件
        var triggerComponent = eventObj.AddComponent<MapTriggerEventComponent>();
        triggerComponent.eventName = eventData.eventName;
        triggerComponent.eventValue = eventData.eventValue;
        triggerComponent.triggerEventType = eventData.triggerEventType;

        // 记录已加载的触发事件
        string key = $"{eventData.eventName}_{eventData.position}";
        _loadedTriggerEvents[key] = new LoadedTriggerEvent
        {
            Instance = eventObj,
            Position = eventData.position,
            Data = eventData
        };
        _loadedTriggerPositions.Add(eventData.position);

        await UniTask.Yield();
    }

    /// <summary>
    /// 清理所有已加载的对象
    /// </summary>
    public void ClearAllLoadedObjects()
    {
        // 清理运行时障碍物
        foreach (var obstacle in _loadedObstacles.Values)
        {
            if (obstacle.Instance != null)
            {
                GameObject.Destroy(obstacle.Instance);
            }
        }
        _loadedObstacles.Clear();
        _loadedObstaclePositions.Clear();

        // 清理触发事件
        foreach (var triggerEvent in _loadedTriggerEvents.Values)
        {
            if (triggerEvent.Instance != null)
            {
                GameObject.Destroy(triggerEvent.Instance);
            }
        }
        _loadedTriggerEvents.Clear();
        _loadedTriggerPositions.Clear();

        _lastCenter = null;
        Debug.Log("MapEventObjectLoader: 已清理所有加载的对象");
    }

    /// <summary>
    /// 获取已加载的运行时障碍物数量
    /// </summary>
    public int GetLoadedObstacleCount()
    {
        return _loadedObstacles.Count;
    }

    /// <summary>
    /// 获取已加载的触发事件数量
    /// </summary>
    public int GetLoadedTriggerEventCount()
    {
        return _loadedTriggerEvents.Count;
    }

    /// <summary>
    /// 根据名称获取已加载的运行时障碍物
    /// </summary>
    public GameObject GetLoadedObstacle(string obstacleName)
    {
        var obstacle = _loadedObstacles.Values.FirstOrDefault(o => o.Data.name == obstacleName);
        return obstacle?.Instance;
    }

    /// <summary>
    /// 根据名称获取已加载的触发事件
    /// </summary>
    public GameObject GetLoadedTriggerEvent(string eventName)
    {
        var triggerEvent = _loadedTriggerEvents.Values.FirstOrDefault(e => e.Data.eventName == eventName);
        return triggerEvent?.Instance;
    }

    /// <summary>
    /// 强制更新指定位置的对象（用于动态数据更新）
    /// </summary>
    public async UniTask ForceUpdateAtPosition(Vector3 worldPosition)
    {
        if (_isLoading) return;

        _lastCenter = null; // 重置缓存，强制更新
        await LoadNearbyEventObjectsAsync(worldPosition);
    }
}
