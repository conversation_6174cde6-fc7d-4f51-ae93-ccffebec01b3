using UnityEngine;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using System.Linq;

public class NPCLoader
{
    // private NinCharacter characterPrefab;
    private Transform npcContainer;
    private Transform mapReference;
    private float loadRadius = 50f;
    private float unloadRadius => loadRadius * 1.5f;

    private List<MapNPCData> _allNPCs = new();
    private Dictionary<string, LoadedNPC> _loadedNPCs = new();
    private HashSet<Vector3> _loadedPositions = new();

    private Vector3? _lastCenter = null;
    private Dictionary<string, MainServer.NpcRoleConfig> changedNpcConfigs = new();
    private List<MainServer.ThroughPointInfo> _throughPoints = new();
    private Vector3? _lastLoadWorldPosition = null;

    public NPCLoader(Transform mapReference, Transform container, List<MapNPCData> npcDataList)
    {
        npcContainer = container;
        this.mapReference = mapReference;
        _allNPCs = npcDataList;
        // LoadNPCData(npcJsonName);
    }

    public void LoadNearbyNPCs(Vector3 worldPosition)
    {
        AsyncLoadNearbyNPCs(worldPosition).Forget();
    }

    private async UniTask AsyncLoadNearbyNPCs(Vector3 worldPosition)
    {
        _lastLoadWorldPosition = worldPosition;
        await UniTask.Yield();
        Vector3 localPos = mapReference.InverseTransformPoint(worldPosition);

        if (!_lastCenter.HasValue || Vector3.Distance(localPos, _lastCenter.Value) > loadRadius)
        {
            _lastCenter = localPos;

            foreach (var npcData in _allNPCs)
            {
                float dist = Vector3.Distance(localPos, npcData.position);
                if (dist <= loadRadius && !_loadedPositions.Contains(npcData.position))
                {
                    await SpawnNPC(npcData);
                }
            }

            UnloadFarNPCs(localPos);
        }
    }

    private async UniTask SpawnNPC(MapNPCData data)
    {
        Vector3 localPos = mapReference.InverseTransformPoint(data.position);
        MainServer.NpcRoleConfig npcConfig = null;
        LoadedNPC loadedNPC = null;
        if(_loadedNPCs.TryGetValue(data.name, out loadedNPC)) {
            npcConfig = loadedNPC.roleConfig;
        }
        if(npcConfig == null) {
            npcConfig = LocalNPC.GetNpcConfig(data.name);
            if(npcConfig != null) {
                npcConfig.IsHiden = data.defaultIsHiden;
            }
        }
        if(npcConfig != null && data.oprationDatas != null && data.oprationDatas.Count > 0) {
            for(int i=0; i<_throughPoints.Count; i++) {
                var point = _throughPoints[i];
                var oprationData = data.oprationDatas.Find(item => item.yarnTitle == point.PointTitle);
                if(oprationData != null) {
                    switch(point.InfoType) {
                        case MainServer.ThroughPointInfoType.Start:
                            switch(oprationData.yarnStartOprationType) {
                                case MapYarnOprationType.Hide:
                                    npcConfig.IsHiden = true;
                                    if(loadedNPC != null) { //已经加载过这个npc
                                        RemoveNpc(loadedNPC);
                                    }
                                    break;
                                case MapYarnOprationType.Show:
                                    npcConfig.IsHiden = false;
                                    break;
                                case MapYarnOprationType.ChangeStatus:
                                    //展示没有
                                    break;
                            }
                            break;
                        case MainServer.ThroughPointInfoType.End:
                            switch(oprationData.yarnEndOprationType) {
                                case MapYarnOprationType.Hide:
                                    npcConfig.IsHiden = true;
                                    if(loadedNPC != null) { //已经加载过这个npc
                                        RemoveNpc(loadedNPC);
                                    }
                                    break;
                                case MapYarnOprationType.Show:
                                    npcConfig.IsHiden = false;
                                    break;
                                case MapYarnOprationType.ChangeStatus:
                                    //展示没有
                                    break;
                            }
                            break;
                    }

                }
            }
            // for(int i=0; i<data.yarnTitleList.Count; i++) {

            //     if(_throughPoints.Contains(data.yarnTitleList[i])) {
            //         // switch(data.yarnStartOprationTypeList[i]) {
            //         //     case MapYarnOprationType.Hide:
            //         //         npcConfig.IsHiden = true;
            //         //         if(loadedNPC != null) { //已经加载过这个npc
            //         //             RemoveNpc(loadedNPC);
            //         //         }
            //         //         break;
            //         //     case MapYarnOprationType.Show:
            //         //         npcConfig.IsHiden = false;
            //         //         break;
            //         //     case MapYarnOprationType.ChangeStatus:
            //         //         //展示没有
            //         //         break;
            //         // }
            //         // switch(data.yarnEndOprationTypeList[i]) {
            //         //     case MapYarnOprationType.Hide:
            //         //         npcConfig.IsHiden = true;
            //         //         if(loadedNPC != null) { //已经加载过这个npc
            //         //             RemoveNpc(loadedNPC);
            //         //         }
            //         //         break;
            //         //     case MapYarnOprationType.Show:
            //         //         npcConfig.IsHiden = false;
            //         //         break;
            //         //     case MapYarnOprationType.ChangeStatus:
            //         //         //展示没有
            //         //         break;
            //         // }
            //     }
            // }
            // _throughPoints.ForEach(item => {
            //     if(data.yarnTitleList.Contains(item)) {
            //         npcConfig.IsHiden = true;
            //     }
            // });
        }
        if(changedNpcConfigs.ContainsKey(data.name)) {
            npcConfig = changedNpcConfigs[data.name];
        }
        if(npcConfig == null || (npcConfig != null && npcConfig.IsHiden)) {
            return;
        }
        if(loadedNPC != null) {
            _loadedPositions.Add(data.position);
        }
        NinCharacter npc = await MapController.Current.mapCharacterMgr.CreateNpc(npcConfig);
        // npc.transform.position = localPos;
        // NinCharacter npc = GameObject.Instantiate(characterPrefab, localPos, Quaternion.identity);
        npc.transform.SetParent(npcContainer, false);
        npc.gameObject.SetActive(true);
        await UniTask.Yield();
        MapController.Current.transferMapMgr.MoveToCurrentMap(npc.ninChatacterTransfer, localPos);
        // npc.ninChatacterTransfer.Transfer(localPos, null);
        // npc.InitNpc(LocalNPC.HeartGoldHealingNpc);
        _loadedNPCs[npcConfig.Name] = new LoadedNPC { Instance = npc, Position = data.position, roleConfig = npcConfig };
        // _loadedNPCs.Add(new LoadedNPC { Instance = npc, Position = data.position });
        _loadedPositions.Add(data.position);
    }
    //全量
    public async UniTask UpdateThroughPoints(List<MainServer.ThroughPointInfo> throughPoints) {
        _throughPoints = throughPoints;
        _lastCenter = null;
        _loadedPositions.Clear();
        if(_lastLoadWorldPosition.HasValue) {
            await AsyncLoadNearbyNPCs(_lastLoadWorldPosition.Value);
        }
        // foreach (var npcData in _allNPCs)
        // {
        //     if(npcData.yarnTitleList != null && npcData.yarnTitleList.Count > 0) {
        //         for(int i=0; i<npcData.yarnTitleList.Count; i++) {
        //             if(throughPoints.Contains(npcData.yarnTitleList[i])) {
        //                 switch(npcData.yarnOprationTypeList[i]) {
        //                     case MapYarnOprationType.Hide:
        //                         break;
        //                     case MapYarnOprationType.Show:
        //                         break;
        //                     case MapYarnOprationType.ChangeStatus:
        //                         break;
        //                 }
        //             }
        //         }
        //         // foreach (var item in throughPoints)
        //         // {
                    
        //         //     npcData.yarnTitleList.Contains(npcData.)
        //         // }
        //     }
        //     // if(_throughPoints.Contains(npcData.yarnTitleList)) {
        //     //     RemoveNpc(item);
        //     // }
        // }
    }
    public void UpdateNpcInfos(List<MainServer.NpcRoleConfig> roleConfigs) {
        // foreach (var item in roleConfigs)
        // {
        //     changedNpcConfigs[item.Name] = item;
        // }
        // foreach (var item in _loadedNPCs)
        // {
        //     if(changedNpcConfigs.TryGetValue(item.Instance.characterName, out var roleConfig)) {
        //         if(roleConfig.IsHiden) {
        //             RemoveNpc(item);
        //         }
        //     }
        // }
    }

    private void UnloadFarNPCs(Vector3 localPos)
    {
        var npcList = _loadedNPCs.Values.ToList();
        for (int i = npcList.Count - 1; i >= 0; i--)
        {
            var npc = npcList[i];
            if (Vector3.Distance(localPos, npc.Position) > unloadRadius)
            {
                RemoveNpc(npc);
            }
        }
    }
    private void RemoveNpc(LoadedNPC npc) {
        MapController.Current.mapCharacterMgr.RemoveNpc(npc.Instance);
        _loadedPositions.Remove(npc.Position);
        _loadedNPCs.Remove(npc.roleConfig.Name);
    }

    // private void LoadNPCData(string npcJsonName)
    // {
    //     _allNPCs.Clear();
    //     var json = AssertResourceLoader.Share.LoadAsset<TextAsset>(npcJsonName).text;
    //     var list = JsonUtility.FromJson<NPCDataList>(json);
    //     _allNPCs.AddRange(list.data);
    // }

    // [System.Serializable]
    // private class NPCDataList
    // {
    //     public List<NPCData> data;
    // }

    // [System.Serializable]
    // private class NPCData
    // {
    //     public string name;
    //     public Vector3 position;
    // }

    private class LoadedNPC
    {
        public NinCharacter Instance;
        public Vector3 Position;
        public MainServer.NpcRoleConfig roleConfig;
    }
}
