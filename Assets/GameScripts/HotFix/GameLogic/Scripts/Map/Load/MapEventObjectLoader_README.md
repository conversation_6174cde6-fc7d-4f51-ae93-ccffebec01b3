# MapEventObjectLoader 使用说明

## 概述

MapEventObjectLoader 是一个地图事件对象加载器，负责动态加载运行时障碍物（RuntimeObstacles）和触发事件组件（TriggerEventComponents）。它参考了 NPCLoader 和 PrefabRegionLoader 的设计模式，支持基于位置的动态加载和卸载。

## 主要功能

### 1. 运行时障碍物加载
- 根据 UnifiedMapExporter 导出的 `runtimeObstacles` 数据动态创建障碍物
- 支持 MapRuntimeObstacle 组件的完整配置
- 支持 Yarn 操作数据的设置
- 支持默认隐藏状态控制

### 2. 触发事件组件加载
- 根据 UnifiedMapExporter 导出的 `triggerEventComponents` 数据动态创建触发器
- 自动添加 BoxCollider 作为触发器
- 支持不同类型的触发事件（Yarn、Weather等）
- 支持事件名称和值的配置

### 3. 动态加载机制
- 基于距离的智能加载/卸载
- 避免重复加载同一位置的对象
- 支持异步加载，不阻塞主线程
- 可配置的加载半径

## 集成方式

MapEventObjectLoader 已经集成到 MapLoader 中：

```csharp
// 在 MapLoader.ConfigMap() 中初始化
_mapEventObjectLoader = new MapEventObjectLoader(
    this.transform,
    mapInfo.GetMapData().runtimeObstacles,
    mapInfo.GetMapData().triggerEventComponents
);

// 在 LoadMap() 和 AsyncLoadMap() 中调用
_mapEventObjectLoader?.LoadNearbyEventObjects(position);
await _mapEventObjectLoader.LoadNearbyEventObjectsAsync(position);
```

## 数据结构

### MapRuntimeObstacleData
```csharp
public class MapRuntimeObstacleData
{
    public string name;                           // 障碍物名称
    public Vector3 position;                      // 位置
    public Vector3 scale;                         // 缩放
    public bool defaultIsHiden;                   // 默认是否隐藏
    public List<MapYarnOprationData> oprationDatas; // Yarn操作数据
}
```

### MapTriggerEventComponentInfo
```csharp
public class MapTriggerEventComponentInfo
{
    public string eventName;                      // 事件名称
    public string eventValue;                     // 事件值
    public MapTriggerEventType triggerEventType;  // 触发事件类型
    public Vector3 position;                      // 位置
    public Vector3 localScale;                    // 缩放
    public Vector3 boxColliderSize;               // 碰撞器大小
}
```

## 使用示例

### 1. 获取已加载的对象
```csharp
// 通过 MapLoader 获取
var mapLoader = MapController.Current.mapLoader;

// 获取运行时障碍物
GameObject obstacle = mapLoader.GetLoadedObstacle("障碍物名称");

// 获取触发事件
GameObject triggerEvent = mapLoader.GetLoadedTriggerEvent("事件名称");

// 获取加载数量统计
var (obstacleCount, triggerCount) = mapLoader.GetLoadedEventObjectCounts();
```

### 2. 强制更新指定位置
```csharp
// 当数据发生变化时，强制更新指定位置的对象
await mapLoader.ForceUpdateEventObjectsAtPosition(worldPosition);
```

## 特性

### 1. 智能加载
- 只在需要时加载，避免性能浪费
- 自动卸载远距离对象，节省内存
- 支持缓存机制，避免频繁重复加载

### 2. 完整的组件支持
- 运行时障碍物自动添加 MapRuntimeObstacle 组件
- 触发事件自动添加 MapTriggerEventComponent 和 BoxCollider
- 支持多个 Yarn 操作数据的配置

### 3. 与现有系统兼容
- 无需额外文件，直接使用 UnifiedMapExporter 导出的数据
- 与 NPCLoader 和 PrefabRegionLoader 使用相同的加载模式
- 集成到现有的 MapLoader 系统中

## 注意事项

1. **数据来源**: 所有数据都来自 UnifiedMapExporter 导出的 JSON 文件，不需要额外的配置文件
2. **性能优化**: 加载器会自动管理对象的生命周期，避免内存泄漏
3. **坐标系统**: 使用本地坐标系统，与地图容器保持一致
4. **异步加载**: 支持异步加载，避免阻塞主线程
5. **动态更新**: 支持运行时数据更新，可以响应 Yarn 脚本的状态变化

## 扩展性

MapEventObjectLoader 设计为可扩展的：
- 可以轻松添加新的事件对象类型
- 支持自定义加载逻辑
- 可以与其他系统（如 Yarn Spinner）集成
- 支持动态数据更新和状态同步
