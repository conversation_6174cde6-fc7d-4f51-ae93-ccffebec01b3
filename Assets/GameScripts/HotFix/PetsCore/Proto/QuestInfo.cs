// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: MainServer/QuestInfo.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace MainServer {

  /// <summary>Holder for reflection information generated from MainServer/QuestInfo.proto</summary>
  public static partial class QuestInfoReflection {

    #region Descriptor
    /// <summary>File descriptor for MainServer/QuestInfo.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static QuestInfoReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChpNYWluU2VydmVyL1F1ZXN0SW5mby5wcm90bxIKTWFpblNlcnZlchoaTWFp",
            "blNlcnZlci9RdWVzdFR5cGUucHJvdG8aHk1haW5TZXJ2ZXIvVHJhaW5lclN0",
            "cmljdC5wcm90byKIBQoNUXVlc3RJbmZvTGlzdBIqCgtxdWVzdF9pbmZvcxgB",
            "IAMoCzIVLk1haW5TZXJ2ZXIuUXVlc3RJbmZvEjIKDXF1ZXN0X3N0cmljdHMY",
            "AiADKAsyGy5NYWluU2VydmVyLlF1ZXN0U3RyaWN0SW5mbxJHChBxdWVzdF91",
            "bmxvY2tfbWFwGAMgAygLMi0uTWFpblNlcnZlci5RdWVzdEluZm9MaXN0LlF1",
            "ZXN0VW5sb2NrTWFwRW50cnkSSwoScXVlc3RfY29tcGxldGVfbWFwGAQgAygL",
            "Mi8uTWFpblNlcnZlci5RdWVzdEluZm9MaXN0LlF1ZXN0Q29tcGxldGVNYXBF",
            "bnRyeRJJChFxdWVzdF9yZXdhcmRzX21hcBgFIAMoCzIuLk1haW5TZXJ2ZXIu",
            "UXVlc3RJbmZvTGlzdC5RdWVzdFJld2FyZHNNYXBFbnRyeRI1ChFxdWVzdF90",
            "eXBlX3ZhbHVlcxgGIAMoCzIaLk1haW5TZXJ2ZXIuUXVlc3RUeXBlVmFsdWUa",
            "UgoTUXVlc3RVbmxvY2tNYXBFbnRyeRILCgNrZXkYASABKAUSKgoFdmFsdWUY",
            "AiABKAsyGy5NYWluU2VydmVyLlF1ZXN0VW5sb2NrSW5mbzoCOAEaVgoVUXVl",
            "c3RDb21wbGV0ZU1hcEVudHJ5EgsKA2tleRgBIAEoBRIsCgV2YWx1ZRgCIAEo",
            "CzIdLk1haW5TZXJ2ZXIuUXVlc3RDb21wbGV0ZUluZm86AjgBGlMKFFF1ZXN0",
            "UmV3YXJkc01hcEVudHJ5EgsKA2tleRgBIAEoBRIqCgV2YWx1ZRgCIAEoCzIb",
            "Lk1haW5TZXJ2ZXIuUXVlc3RSZXdhcmRJbmZvOgI4ASLIBgoJUXVlc3RJbmZv",
            "EhAKCHF1ZXN0X2lkGAEgASgFEikKCnF1ZXN0X3R5cGUYAiABKA4yFS5NYWlu",
            "U2VydmVyLlF1ZXN0VHlwZRITCgtxdWVzdF9sZXZlbBgDIAEoBRItCgxxdWVz",
            "dF9zdGF0dXMYBCABKA4yFy5NYWluU2VydmVyLlF1ZXN0U3RhdHVzEjYKEXF1",
            "ZXN0X3VubG9ja19pbmZvGAUgASgLMhsuTWFpblNlcnZlci5RdWVzdFVubG9j",
            "a0luZm8SMAoNbGluZWFyX3F1ZXN0cxgGIAEoCzIZLk1haW5TZXJ2ZXIuUXVl",
            "c3RMaXN0SW5mbxIUCgxzaW5nbGVfcXVlc3QYByABKAgSMQoOY3VycmVudF9x",
            "dWVzdHMYCCABKAsyGS5NYWluU2VydmVyLlF1ZXN0TGlzdEluZm8SMQoMcXVl",
            "c3Rfc3RyaWN0GAkgASgLMhsuTWFpblNlcnZlci5RdWVzdFN0cmljdEluZm8S",
            "NgoRcXVlc3RfcmV3YXJkX2luZm8YCiABKAsyGy5NYWluU2VydmVyLlF1ZXN0",
            "UmV3YXJkSW5mbxIYChBxdWVzdF9zdGFydF90aW1lGAsgASgDEhYKDnF1ZXN0",
            "X2VuZF90aW1lGAwgASgDEhoKEnF1ZXN0X3JlcGVhdF9saW1pdBgNIAEoBRId",
            "ChVxdWVzdF9yZXBlYXRfaW50ZXJ2YWwYDiABKAUSNwoPcXVlc3RfYnJvYWRj",
            "YXN0GA8gASgLMh4uTWFpblNlcnZlci5RdWVzdEJyb2FkY2FzdEluZm8SOgoT",
            "cXVlc3RfY29tcGxldGVfaW5mbxgQIAEoCzIdLk1haW5TZXJ2ZXIuUXVlc3RD",
            "b21wbGV0ZUluZm8SHwoXcXVlc3RfcmVwZWF0X2xpbWl0X3RpbWUYESABKAUS",
            "DwoHdmVyc2lvbhgSIAEoBRI2ChFxdWVzdF9jYW5jZWxfaW5mbxgTIAEoCzIb",
            "Lk1haW5TZXJ2ZXIuUXVlc3RDYW5jZWxJbmZvEh4KFnF1ZXN0X2RlcGVuZF9w",
            "YXJlbnRfaWQYFCABKAUSMAoRcGFyZW50X3F1ZXN0X2luZm8YFSABKAsyFS5N",
            "YWluU2VydmVyLlF1ZXN0SW5mbyJKCg9RdWVzdENhbmNlbEluZm8SFwoPY2Fu",
            "Y2VsX2ludGVydmFsGAEgASgFEh4KFmNhbmNlbF9hY2NlcHRfaW50ZXJ2YWwY",
            "AiABKAUiYAoNUXVlc3RMaXN0SW5mbxIQCghoYXNfbGlzdBgBIAEoCBIRCglx",
            "dWVzdF9pZHMYAiADKAUSKgoLcXVlc3RfaW5mb3MYAyADKAsyFS5NYWluU2Vy",
            "dmVyLlF1ZXN0SW5mbyJcCg9RdWVzdFN0cmljdEluZm8SFwoPcXVlc3Rfc3Ry",
            "aWN0X2lkGAEgASgFEjAKDXF1ZXN0X3N0cmljdHMYAiADKAsyGS5NYWluU2Vy",
            "dmVyLlRyYWluZXJTdHJpY3QicQoPUXVlc3RVbmxvY2tJbmZvEhcKD3F1ZXN0",
            "X3VubG9ja19pZBgBIAEoBRJFChdxdWVzdF91bmxvY2tfY29uZGl0aW9ucxgC",
            "IAMoCzIkLk1haW5TZXJ2ZXIuUXVlc3RVbmxvY2tDb25kaXRpb25JbmZvIosB",
            "ChhRdWVzdFVubG9ja0NvbmRpdGlvbkluZm8SNgoRcXVlc3RfdW5sb2NrX3R5",
            "cGUYASABKA4yGy5NYWluU2VydmVyLlF1ZXN0VW5sb2NrVHlwZRI3Cg9xdWVz",
            "dF9jb25kaXRpb24YAiABKAsyHi5NYWluU2VydmVyLlF1ZXN0Q29uZGl0aW9u",
            "SW5mbyKRAQoSUXVlc3RDb25kaXRpb25JbmZvEhkKEWNvbmRpdGlvbl9uYW1l",
            "X2lkGAEgASgJEhcKD2NvbmRpdGlvbl9jb3VudBgCIAEoBRISCgpqc29uX3Zh",
            "bHVlGAMgASgJEhIKCnRpbWVfbGltaXQYBCABKAUSDwoHaXNfdXNlZBgFIAEo",
            "CBIOCgZpc19hZGQYBiABKAgikQEKGlF1ZXN0Q29tcGxldGVDb25kaXRpb25J",
            "bmZvEjoKE3F1ZXN0X2NvbXBsZXRlX3R5cGUYASABKA4yHS5NYWluU2VydmVy",
            "LlF1ZXN0Q29tcGxldGVUeXBlEjcKD3F1ZXN0X2NvbmRpdGlvbhgCIAEoCzIe",
            "Lk1haW5TZXJ2ZXIuUXVlc3RDb25kaXRpb25JbmZvIp0BChFRdWVzdENvbXBs",
            "ZXRlSW5mbxIZChFxdWVzdF9jb21wbGV0ZV9pZBgBIAEoBRJJChlxdWVzdF9j",
            "b21wbGV0ZV9jb25kaXRpb25zGAIgAygLMiYuTWFpblNlcnZlci5RdWVzdENv",
            "bXBsZXRlQ29uZGl0aW9uSW5mbxIiChpxdWVzdF9jb21wbGV0ZV9yZXBvcnRf",
            "bmFtZRgDIAEoCSJ1Cg9RdWVzdFJld2FyZEluZm8SFwoPcXVlc3RfcmV3YXJk",
            "X2lkGAEgASgFEjMKDXF1ZXN0X3Jld2FyZHMYAiADKAsyHC5NYWluU2VydmVy",
            "LlF1ZXN0UmV3YXJkVmFsdWUSFAoMcmFuZG9tX2NvdW50GAMgASgFIqcCChBR",
            "dWVzdFJld2FyZFZhbHVlEjYKEXF1ZXN0X3Jld2FyZF90eXBlGAEgASgOMhsu",
            "TWFpblNlcnZlci5RdWVzdFJld2FyZFR5cGUSGQoRcXVlc3RfcmV3YXJkX3Jh",
            "dGUYAiABKAISGgoScXVlc3RfcmV3YXJkX2NvdW50GAMgASgFEkoKF3F1ZXN0",
            "X3Jld2FyZF9jb3VudF9yYXRlGAQgASgOMikuTWFpblNlcnZlci5RdWVzdFJl",
            "d2FyZFZhbHVlQ291bnRSYXRlVHlwZRIbChNkYXlfd2hvbGVfbmV0bG9ja2Vk",
            "GAUgASgFEhoKEnF1ZXN0X3Jld2FyZF92YWx1ZRgGIAEoCRIfChdxdWVzdF9y",
            "ZXdhcmRfYmFzZV9tb25leRgHIAEoBSJxChJRdWVzdEJyb2FkY2FzdEluZm8S",
            "PAoUcXVlc3RfYnJvYWRjYXN0X3R5cGUYASABKA4yHi5NYWluU2VydmVyLlF1",
            "ZXN0QnJvYWRjYXN0VHlwZRIdChVxdWVzdF9icm9hZGNhc3RfdmFsdWUYAiAB",
            "KAkiwQEKDlF1ZXN0VHlwZVZhbHVlEhsKE3F1ZXN0X3R5cGVfdmFsdWVfaWQY",
            "ASABKAUSGAoQcXVlc3RfdHlwZV92YWx1ZRgCIAEoAxIaChJpc19zdHJpbmdf",
            "dmFsdWVfaWQYAyABKAgSFwoPaXNfc3RyaW5nX3ZhbHVlGAQgASgIEh8KF3F1",
            "ZXN0X3R5cGVfdmFsdWVfc3RyaW5nGAUgASgJEiIKGnF1ZXN0X3R5cGVfdmFs",
            "dWVfc3RyaW5nX2lkGAYgASgJQiFaH2dvLW5ha2FtYS1wb2tlL3Byb3RvL01h",
            "aW5TZXJ2ZXJiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::MainServer.QuestTypeReflection.Descriptor, global::MainServer.TrainerStrictReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestInfoList), global::MainServer.QuestInfoList.Parser, new[]{ "QuestInfos", "QuestStricts", "QuestUnlockMap", "QuestCompleteMap", "QuestRewardsMap", "QuestTypeValues" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, null, null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestInfo), global::MainServer.QuestInfo.Parser, new[]{ "QuestId", "QuestType", "QuestLevel", "QuestStatus", "QuestUnlockInfo", "LinearQuests", "SingleQuest", "CurrentQuests", "QuestStrict", "QuestRewardInfo", "QuestStartTime", "QuestEndTime", "QuestRepeatLimit", "QuestRepeatInterval", "QuestBroadcast", "QuestCompleteInfo", "QuestRepeatLimitTime", "Version", "QuestCancelInfo", "QuestDependParentId", "ParentQuestInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestCancelInfo), global::MainServer.QuestCancelInfo.Parser, new[]{ "CancelInterval", "CancelAcceptInterval" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestListInfo), global::MainServer.QuestListInfo.Parser, new[]{ "HasList", "QuestIds", "QuestInfos" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestStrictInfo), global::MainServer.QuestStrictInfo.Parser, new[]{ "QuestStrictId", "QuestStricts" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestUnlockInfo), global::MainServer.QuestUnlockInfo.Parser, new[]{ "QuestUnlockId", "QuestUnlockConditions" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestUnlockConditionInfo), global::MainServer.QuestUnlockConditionInfo.Parser, new[]{ "QuestUnlockType", "QuestCondition" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestConditionInfo), global::MainServer.QuestConditionInfo.Parser, new[]{ "ConditionNameId", "ConditionCount", "JsonValue", "TimeLimit", "IsUsed", "IsAdd" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestCompleteConditionInfo), global::MainServer.QuestCompleteConditionInfo.Parser, new[]{ "QuestCompleteType", "QuestCondition" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestCompleteInfo), global::MainServer.QuestCompleteInfo.Parser, new[]{ "QuestCompleteId", "QuestCompleteConditions", "QuestCompleteReportName" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestRewardInfo), global::MainServer.QuestRewardInfo.Parser, new[]{ "QuestRewardId", "QuestRewards", "RandomCount" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestRewardValue), global::MainServer.QuestRewardValue.Parser, new[]{ "QuestRewardType", "QuestRewardRate", "QuestRewardCount", "QuestRewardCountRate", "DayWholeNetlocked", "QuestRewardValue_", "QuestRewardBaseMoney" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestBroadcastInfo), global::MainServer.QuestBroadcastInfo.Parser, new[]{ "QuestBroadcastType", "QuestBroadcastValue" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.QuestTypeValue), global::MainServer.QuestTypeValue.Parser, new[]{ "QuestTypeValueId", "QuestTypeValue_", "IsStringValueId", "IsStringValue", "QuestTypeValueString", "QuestTypeValueStringId" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestInfoList : pb::IMessage<QuestInfoList>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestInfoList> _parser = new pb::MessageParser<QuestInfoList>(() => new QuestInfoList());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestInfoList> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestInfoList() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestInfoList(QuestInfoList other) : this() {
      questInfos_ = other.questInfos_.Clone();
      questStricts_ = other.questStricts_.Clone();
      questUnlockMap_ = other.questUnlockMap_.Clone();
      questCompleteMap_ = other.questCompleteMap_.Clone();
      questRewardsMap_ = other.questRewardsMap_.Clone();
      questTypeValues_ = other.questTypeValues_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestInfoList Clone() {
      return new QuestInfoList(this);
    }

    /// <summary>Field number for the "quest_infos" field.</summary>
    public const int QuestInfosFieldNumber = 1;
    private static readonly pb::FieldCodec<global::MainServer.QuestInfo> _repeated_questInfos_codec
        = pb::FieldCodec.ForMessage(10, global::MainServer.QuestInfo.Parser);
    private readonly pbc::RepeatedField<global::MainServer.QuestInfo> questInfos_ = new pbc::RepeatedField<global::MainServer.QuestInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::MainServer.QuestInfo> QuestInfos {
      get { return questInfos_; }
    }

    /// <summary>Field number for the "quest_stricts" field.</summary>
    public const int QuestStrictsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::MainServer.QuestStrictInfo> _repeated_questStricts_codec
        = pb::FieldCodec.ForMessage(18, global::MainServer.QuestStrictInfo.Parser);
    private readonly pbc::RepeatedField<global::MainServer.QuestStrictInfo> questStricts_ = new pbc::RepeatedField<global::MainServer.QuestStrictInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::MainServer.QuestStrictInfo> QuestStricts {
      get { return questStricts_; }
    }

    /// <summary>Field number for the "quest_unlock_map" field.</summary>
    public const int QuestUnlockMapFieldNumber = 3;
    private static readonly pbc::MapField<int, global::MainServer.QuestUnlockInfo>.Codec _map_questUnlockMap_codec
        = new pbc::MapField<int, global::MainServer.QuestUnlockInfo>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::MainServer.QuestUnlockInfo.Parser), 26);
    private readonly pbc::MapField<int, global::MainServer.QuestUnlockInfo> questUnlockMap_ = new pbc::MapField<int, global::MainServer.QuestUnlockInfo>();
    /// <summary>
    /// repeated QuestUnlockInfo quest_unlocks = 3;
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, global::MainServer.QuestUnlockInfo> QuestUnlockMap {
      get { return questUnlockMap_; }
    }

    /// <summary>Field number for the "quest_complete_map" field.</summary>
    public const int QuestCompleteMapFieldNumber = 4;
    private static readonly pbc::MapField<int, global::MainServer.QuestCompleteInfo>.Codec _map_questCompleteMap_codec
        = new pbc::MapField<int, global::MainServer.QuestCompleteInfo>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::MainServer.QuestCompleteInfo.Parser), 34);
    private readonly pbc::MapField<int, global::MainServer.QuestCompleteInfo> questCompleteMap_ = new pbc::MapField<int, global::MainServer.QuestCompleteInfo>();
    /// <summary>
    /// repeated QuestCompleteInfo quest_completes = 4;
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, global::MainServer.QuestCompleteInfo> QuestCompleteMap {
      get { return questCompleteMap_; }
    }

    /// <summary>Field number for the "quest_rewards_map" field.</summary>
    public const int QuestRewardsMapFieldNumber = 5;
    private static readonly pbc::MapField<int, global::MainServer.QuestRewardInfo>.Codec _map_questRewardsMap_codec
        = new pbc::MapField<int, global::MainServer.QuestRewardInfo>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::MainServer.QuestRewardInfo.Parser), 42);
    private readonly pbc::MapField<int, global::MainServer.QuestRewardInfo> questRewardsMap_ = new pbc::MapField<int, global::MainServer.QuestRewardInfo>();
    /// <summary>
    /// repeated QuestRewardInfo quest_rewards = 5;
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, global::MainServer.QuestRewardInfo> QuestRewardsMap {
      get { return questRewardsMap_; }
    }

    /// <summary>Field number for the "quest_type_values" field.</summary>
    public const int QuestTypeValuesFieldNumber = 6;
    private static readonly pb::FieldCodec<global::MainServer.QuestTypeValue> _repeated_questTypeValues_codec
        = pb::FieldCodec.ForMessage(50, global::MainServer.QuestTypeValue.Parser);
    private readonly pbc::RepeatedField<global::MainServer.QuestTypeValue> questTypeValues_ = new pbc::RepeatedField<global::MainServer.QuestTypeValue>();
    /// <summary>
    /// quest值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::MainServer.QuestTypeValue> QuestTypeValues {
      get { return questTypeValues_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestInfoList);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestInfoList other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!questInfos_.Equals(other.questInfos_)) return false;
      if(!questStricts_.Equals(other.questStricts_)) return false;
      if (!QuestUnlockMap.Equals(other.QuestUnlockMap)) return false;
      if (!QuestCompleteMap.Equals(other.QuestCompleteMap)) return false;
      if (!QuestRewardsMap.Equals(other.QuestRewardsMap)) return false;
      if(!questTypeValues_.Equals(other.questTypeValues_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= questInfos_.GetHashCode();
      hash ^= questStricts_.GetHashCode();
      hash ^= QuestUnlockMap.GetHashCode();
      hash ^= QuestCompleteMap.GetHashCode();
      hash ^= QuestRewardsMap.GetHashCode();
      hash ^= questTypeValues_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      questInfos_.WriteTo(output, _repeated_questInfos_codec);
      questStricts_.WriteTo(output, _repeated_questStricts_codec);
      questUnlockMap_.WriteTo(output, _map_questUnlockMap_codec);
      questCompleteMap_.WriteTo(output, _map_questCompleteMap_codec);
      questRewardsMap_.WriteTo(output, _map_questRewardsMap_codec);
      questTypeValues_.WriteTo(output, _repeated_questTypeValues_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      questInfos_.WriteTo(ref output, _repeated_questInfos_codec);
      questStricts_.WriteTo(ref output, _repeated_questStricts_codec);
      questUnlockMap_.WriteTo(ref output, _map_questUnlockMap_codec);
      questCompleteMap_.WriteTo(ref output, _map_questCompleteMap_codec);
      questRewardsMap_.WriteTo(ref output, _map_questRewardsMap_codec);
      questTypeValues_.WriteTo(ref output, _repeated_questTypeValues_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += questInfos_.CalculateSize(_repeated_questInfos_codec);
      size += questStricts_.CalculateSize(_repeated_questStricts_codec);
      size += questUnlockMap_.CalculateSize(_map_questUnlockMap_codec);
      size += questCompleteMap_.CalculateSize(_map_questCompleteMap_codec);
      size += questRewardsMap_.CalculateSize(_map_questRewardsMap_codec);
      size += questTypeValues_.CalculateSize(_repeated_questTypeValues_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestInfoList other) {
      if (other == null) {
        return;
      }
      questInfos_.Add(other.questInfos_);
      questStricts_.Add(other.questStricts_);
      questUnlockMap_.MergeFrom(other.questUnlockMap_);
      questCompleteMap_.MergeFrom(other.questCompleteMap_);
      questRewardsMap_.MergeFrom(other.questRewardsMap_);
      questTypeValues_.Add(other.questTypeValues_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            questInfos_.AddEntriesFrom(input, _repeated_questInfos_codec);
            break;
          }
          case 18: {
            questStricts_.AddEntriesFrom(input, _repeated_questStricts_codec);
            break;
          }
          case 26: {
            questUnlockMap_.AddEntriesFrom(input, _map_questUnlockMap_codec);
            break;
          }
          case 34: {
            questCompleteMap_.AddEntriesFrom(input, _map_questCompleteMap_codec);
            break;
          }
          case 42: {
            questRewardsMap_.AddEntriesFrom(input, _map_questRewardsMap_codec);
            break;
          }
          case 50: {
            questTypeValues_.AddEntriesFrom(input, _repeated_questTypeValues_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            questInfos_.AddEntriesFrom(ref input, _repeated_questInfos_codec);
            break;
          }
          case 18: {
            questStricts_.AddEntriesFrom(ref input, _repeated_questStricts_codec);
            break;
          }
          case 26: {
            questUnlockMap_.AddEntriesFrom(ref input, _map_questUnlockMap_codec);
            break;
          }
          case 34: {
            questCompleteMap_.AddEntriesFrom(ref input, _map_questCompleteMap_codec);
            break;
          }
          case 42: {
            questRewardsMap_.AddEntriesFrom(ref input, _map_questRewardsMap_codec);
            break;
          }
          case 50: {
            questTypeValues_.AddEntriesFrom(ref input, _repeated_questTypeValues_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///这个是任务的描述 训练师接的任务信息请查看TrainerQuestInfo
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestInfo : pb::IMessage<QuestInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestInfo> _parser = new pb::MessageParser<QuestInfo>(() => new QuestInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestInfo(QuestInfo other) : this() {
      questId_ = other.questId_;
      questType_ = other.questType_;
      questLevel_ = other.questLevel_;
      questStatus_ = other.questStatus_;
      questUnlockInfo_ = other.questUnlockInfo_ != null ? other.questUnlockInfo_.Clone() : null;
      linearQuests_ = other.linearQuests_ != null ? other.linearQuests_.Clone() : null;
      singleQuest_ = other.singleQuest_;
      currentQuests_ = other.currentQuests_ != null ? other.currentQuests_.Clone() : null;
      questStrict_ = other.questStrict_ != null ? other.questStrict_.Clone() : null;
      questRewardInfo_ = other.questRewardInfo_ != null ? other.questRewardInfo_.Clone() : null;
      questStartTime_ = other.questStartTime_;
      questEndTime_ = other.questEndTime_;
      questRepeatLimit_ = other.questRepeatLimit_;
      questRepeatInterval_ = other.questRepeatInterval_;
      questBroadcast_ = other.questBroadcast_ != null ? other.questBroadcast_.Clone() : null;
      questCompleteInfo_ = other.questCompleteInfo_ != null ? other.questCompleteInfo_.Clone() : null;
      questRepeatLimitTime_ = other.questRepeatLimitTime_;
      version_ = other.version_;
      questCancelInfo_ = other.questCancelInfo_ != null ? other.questCancelInfo_.Clone() : null;
      questDependParentId_ = other.questDependParentId_;
      parentQuestInfo_ = other.parentQuestInfo_ != null ? other.parentQuestInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestInfo Clone() {
      return new QuestInfo(this);
    }

    /// <summary>Field number for the "quest_id" field.</summary>
    public const int QuestIdFieldNumber = 1;
    private int questId_;
    /// <summary>
    /// quest id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestId {
      get { return questId_; }
      set {
        questId_ = value;
      }
    }

    /// <summary>Field number for the "quest_type" field.</summary>
    public const int QuestTypeFieldNumber = 2;
    private global::MainServer.QuestType questType_ = global::MainServer.QuestType.Once;
    /// <summary>
    /// 类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestType QuestType {
      get { return questType_; }
      set {
        questType_ = value;
      }
    }

    /// <summary>Field number for the "quest_level" field.</summary>
    public const int QuestLevelFieldNumber = 3;
    private int questLevel_;
    /// <summary>
    /// 等级 （难度？）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestLevel {
      get { return questLevel_; }
      set {
        questLevel_ = value;
      }
    }

    /// <summary>Field number for the "quest_status" field.</summary>
    public const int QuestStatusFieldNumber = 4;
    private global::MainServer.QuestStatus questStatus_ = global::MainServer.QuestStatus.Close;
    /// <summary>
    /// 状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestStatus QuestStatus {
      get { return questStatus_; }
      set {
        questStatus_ = value;
      }
    }

    /// <summary>Field number for the "quest_unlock_info" field.</summary>
    public const int QuestUnlockInfoFieldNumber = 5;
    private global::MainServer.QuestUnlockInfo questUnlockInfo_;
    /// <summary>
    ///解锁条件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestUnlockInfo QuestUnlockInfo {
      get { return questUnlockInfo_; }
      set {
        questUnlockInfo_ = value;
      }
    }

    /// <summary>Field number for the "linear_quests" field.</summary>
    public const int LinearQuestsFieldNumber = 6;
    private global::MainServer.QuestListInfo linearQuests_;
    /// <summary>
    /// 该任务的线性任务id列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestListInfo LinearQuests {
      get { return linearQuests_; }
      set {
        linearQuests_ = value;
      }
    }

    /// <summary>Field number for the "single_quest" field.</summary>
    public const int SingleQuestFieldNumber = 7;
    private bool singleQuest_;
    /// <summary>
    /// 是否为单任务 //如果是单任务，但是current又有多个则随机 //或者指定（npc选择的时候应该）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool SingleQuest {
      get { return singleQuest_; }
      set {
        singleQuest_ = value;
      }
    }

    /// <summary>Field number for the "current_quests" field.</summary>
    public const int CurrentQuestsFieldNumber = 8;
    private global::MainServer.QuestListInfo currentQuests_;
    /// <summary>
    /// 当前进度要完成的任务列表(非单任务)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestListInfo CurrentQuests {
      get { return currentQuests_; }
      set {
        currentQuests_ = value;
      }
    }

    /// <summary>Field number for the "quest_strict" field.</summary>
    public const int QuestStrictFieldNumber = 9;
    private global::MainServer.QuestStrictInfo questStrict_;
    /// <summary>
    /// 接下这个任务后对玩家的限制
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestStrictInfo QuestStrict {
      get { return questStrict_; }
      set {
        questStrict_ = value;
      }
    }

    /// <summary>Field number for the "quest_reward_info" field.</summary>
    public const int QuestRewardInfoFieldNumber = 10;
    private global::MainServer.QuestRewardInfo questRewardInfo_;
    /// <summary>
    /// QuestRewardInfo quest_reward = 9; // 奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestRewardInfo QuestRewardInfo {
      get { return questRewardInfo_; }
      set {
        questRewardInfo_ = value;
      }
    }

    /// <summary>Field number for the "quest_start_time" field.</summary>
    public const int QuestStartTimeFieldNumber = 11;
    private long questStartTime_;
    /// <summary>
    /// 开始时间(ts 秒级别)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long QuestStartTime {
      get { return questStartTime_; }
      set {
        questStartTime_ = value;
      }
    }

    /// <summary>Field number for the "quest_end_time" field.</summary>
    public const int QuestEndTimeFieldNumber = 12;
    private long questEndTime_;
    /// <summary>
    /// 结束时间(ts 秒级别)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long QuestEndTime {
      get { return questEndTime_; }
      set {
        questEndTime_ = value;
      }
    }

    /// <summary>Field number for the "quest_repeat_limit" field.</summary>
    public const int QuestRepeatLimitFieldNumber = 13;
    private int questRepeatLimit_;
    /// <summary>
    /// 重复限制（一天最多只能完成这么多次）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestRepeatLimit {
      get { return questRepeatLimit_; }
      set {
        questRepeatLimit_ = value;
      }
    }

    /// <summary>Field number for the "quest_repeat_interval" field.</summary>
    public const int QuestRepeatIntervalFieldNumber = 14;
    private int questRepeatInterval_;
    /// <summary>
    /// 重复间隔
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestRepeatInterval {
      get { return questRepeatInterval_; }
      set {
        questRepeatInterval_ = value;
      }
    }

    /// <summary>Field number for the "quest_broadcast" field.</summary>
    public const int QuestBroadcastFieldNumber = 15;
    private global::MainServer.QuestBroadcastInfo questBroadcast_;
    /// <summary>
    /// 广播
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestBroadcastInfo QuestBroadcast {
      get { return questBroadcast_; }
      set {
        questBroadcast_ = value;
      }
    }

    /// <summary>Field number for the "quest_complete_info" field.</summary>
    public const int QuestCompleteInfoFieldNumber = 16;
    private global::MainServer.QuestCompleteInfo questCompleteInfo_;
    /// <summary>
    ///完成条件id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestCompleteInfo QuestCompleteInfo {
      get { return questCompleteInfo_; }
      set {
        questCompleteInfo_ = value;
      }
    }

    /// <summary>Field number for the "quest_repeat_limit_time" field.</summary>
    public const int QuestRepeatLimitTimeFieldNumber = 17;
    private int questRepeatLimitTime_;
    /// <summary>
    /// 限制时间（训练家接受任务后必须要在这个时间内完成任务）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestRepeatLimitTime {
      get { return questRepeatLimitTime_; }
      set {
        questRepeatLimitTime_ = value;
      }
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 18;
    private int version_;
    /// <summary>
    /// 版本号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Version {
      get { return version_; }
      set {
        version_ = value;
      }
    }

    /// <summary>Field number for the "quest_cancel_info" field.</summary>
    public const int QuestCancelInfoFieldNumber = 19;
    private global::MainServer.QuestCancelInfo questCancelInfo_;
    /// <summary>
    /// 取消信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestCancelInfo QuestCancelInfo {
      get { return questCancelInfo_; }
      set {
        questCancelInfo_ = value;
      }
    }

    /// <summary>Field number for the "quest_depend_parent_id" field.</summary>
    public const int QuestDependParentIdFieldNumber = 20;
    private int questDependParentId_;
    /// <summary>
    /// 依赖的父任务id(非dbid)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestDependParentId {
      get { return questDependParentId_; }
      set {
        questDependParentId_ = value;
      }
    }

    /// <summary>Field number for the "parent_quest_info" field.</summary>
    public const int ParentQuestInfoFieldNumber = 21;
    private global::MainServer.QuestInfo parentQuestInfo_;
    /// <summary>
    /// 父任务 //不存入QuestInfo数据库表 //序列化的时候会存入jsonb
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestInfo ParentQuestInfo {
      get { return parentQuestInfo_; }
      set {
        parentQuestInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestId != other.QuestId) return false;
      if (QuestType != other.QuestType) return false;
      if (QuestLevel != other.QuestLevel) return false;
      if (QuestStatus != other.QuestStatus) return false;
      if (!object.Equals(QuestUnlockInfo, other.QuestUnlockInfo)) return false;
      if (!object.Equals(LinearQuests, other.LinearQuests)) return false;
      if (SingleQuest != other.SingleQuest) return false;
      if (!object.Equals(CurrentQuests, other.CurrentQuests)) return false;
      if (!object.Equals(QuestStrict, other.QuestStrict)) return false;
      if (!object.Equals(QuestRewardInfo, other.QuestRewardInfo)) return false;
      if (QuestStartTime != other.QuestStartTime) return false;
      if (QuestEndTime != other.QuestEndTime) return false;
      if (QuestRepeatLimit != other.QuestRepeatLimit) return false;
      if (QuestRepeatInterval != other.QuestRepeatInterval) return false;
      if (!object.Equals(QuestBroadcast, other.QuestBroadcast)) return false;
      if (!object.Equals(QuestCompleteInfo, other.QuestCompleteInfo)) return false;
      if (QuestRepeatLimitTime != other.QuestRepeatLimitTime) return false;
      if (Version != other.Version) return false;
      if (!object.Equals(QuestCancelInfo, other.QuestCancelInfo)) return false;
      if (QuestDependParentId != other.QuestDependParentId) return false;
      if (!object.Equals(ParentQuestInfo, other.ParentQuestInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestId != 0) hash ^= QuestId.GetHashCode();
      if (QuestType != global::MainServer.QuestType.Once) hash ^= QuestType.GetHashCode();
      if (QuestLevel != 0) hash ^= QuestLevel.GetHashCode();
      if (QuestStatus != global::MainServer.QuestStatus.Close) hash ^= QuestStatus.GetHashCode();
      if (questUnlockInfo_ != null) hash ^= QuestUnlockInfo.GetHashCode();
      if (linearQuests_ != null) hash ^= LinearQuests.GetHashCode();
      if (SingleQuest != false) hash ^= SingleQuest.GetHashCode();
      if (currentQuests_ != null) hash ^= CurrentQuests.GetHashCode();
      if (questStrict_ != null) hash ^= QuestStrict.GetHashCode();
      if (questRewardInfo_ != null) hash ^= QuestRewardInfo.GetHashCode();
      if (QuestStartTime != 0L) hash ^= QuestStartTime.GetHashCode();
      if (QuestEndTime != 0L) hash ^= QuestEndTime.GetHashCode();
      if (QuestRepeatLimit != 0) hash ^= QuestRepeatLimit.GetHashCode();
      if (QuestRepeatInterval != 0) hash ^= QuestRepeatInterval.GetHashCode();
      if (questBroadcast_ != null) hash ^= QuestBroadcast.GetHashCode();
      if (questCompleteInfo_ != null) hash ^= QuestCompleteInfo.GetHashCode();
      if (QuestRepeatLimitTime != 0) hash ^= QuestRepeatLimitTime.GetHashCode();
      if (Version != 0) hash ^= Version.GetHashCode();
      if (questCancelInfo_ != null) hash ^= QuestCancelInfo.GetHashCode();
      if (QuestDependParentId != 0) hash ^= QuestDependParentId.GetHashCode();
      if (parentQuestInfo_ != null) hash ^= ParentQuestInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestId);
      }
      if (QuestType != global::MainServer.QuestType.Once) {
        output.WriteRawTag(16);
        output.WriteEnum((int) QuestType);
      }
      if (QuestLevel != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(QuestLevel);
      }
      if (QuestStatus != global::MainServer.QuestStatus.Close) {
        output.WriteRawTag(32);
        output.WriteEnum((int) QuestStatus);
      }
      if (questUnlockInfo_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(QuestUnlockInfo);
      }
      if (linearQuests_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(LinearQuests);
      }
      if (SingleQuest != false) {
        output.WriteRawTag(56);
        output.WriteBool(SingleQuest);
      }
      if (currentQuests_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(CurrentQuests);
      }
      if (questStrict_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(QuestStrict);
      }
      if (questRewardInfo_ != null) {
        output.WriteRawTag(82);
        output.WriteMessage(QuestRewardInfo);
      }
      if (QuestStartTime != 0L) {
        output.WriteRawTag(88);
        output.WriteInt64(QuestStartTime);
      }
      if (QuestEndTime != 0L) {
        output.WriteRawTag(96);
        output.WriteInt64(QuestEndTime);
      }
      if (QuestRepeatLimit != 0) {
        output.WriteRawTag(104);
        output.WriteInt32(QuestRepeatLimit);
      }
      if (QuestRepeatInterval != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(QuestRepeatInterval);
      }
      if (questBroadcast_ != null) {
        output.WriteRawTag(122);
        output.WriteMessage(QuestBroadcast);
      }
      if (questCompleteInfo_ != null) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(QuestCompleteInfo);
      }
      if (QuestRepeatLimitTime != 0) {
        output.WriteRawTag(136, 1);
        output.WriteInt32(QuestRepeatLimitTime);
      }
      if (Version != 0) {
        output.WriteRawTag(144, 1);
        output.WriteInt32(Version);
      }
      if (questCancelInfo_ != null) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(QuestCancelInfo);
      }
      if (QuestDependParentId != 0) {
        output.WriteRawTag(160, 1);
        output.WriteInt32(QuestDependParentId);
      }
      if (parentQuestInfo_ != null) {
        output.WriteRawTag(170, 1);
        output.WriteMessage(ParentQuestInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestId);
      }
      if (QuestType != global::MainServer.QuestType.Once) {
        output.WriteRawTag(16);
        output.WriteEnum((int) QuestType);
      }
      if (QuestLevel != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(QuestLevel);
      }
      if (QuestStatus != global::MainServer.QuestStatus.Close) {
        output.WriteRawTag(32);
        output.WriteEnum((int) QuestStatus);
      }
      if (questUnlockInfo_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(QuestUnlockInfo);
      }
      if (linearQuests_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(LinearQuests);
      }
      if (SingleQuest != false) {
        output.WriteRawTag(56);
        output.WriteBool(SingleQuest);
      }
      if (currentQuests_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(CurrentQuests);
      }
      if (questStrict_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(QuestStrict);
      }
      if (questRewardInfo_ != null) {
        output.WriteRawTag(82);
        output.WriteMessage(QuestRewardInfo);
      }
      if (QuestStartTime != 0L) {
        output.WriteRawTag(88);
        output.WriteInt64(QuestStartTime);
      }
      if (QuestEndTime != 0L) {
        output.WriteRawTag(96);
        output.WriteInt64(QuestEndTime);
      }
      if (QuestRepeatLimit != 0) {
        output.WriteRawTag(104);
        output.WriteInt32(QuestRepeatLimit);
      }
      if (QuestRepeatInterval != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(QuestRepeatInterval);
      }
      if (questBroadcast_ != null) {
        output.WriteRawTag(122);
        output.WriteMessage(QuestBroadcast);
      }
      if (questCompleteInfo_ != null) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(QuestCompleteInfo);
      }
      if (QuestRepeatLimitTime != 0) {
        output.WriteRawTag(136, 1);
        output.WriteInt32(QuestRepeatLimitTime);
      }
      if (Version != 0) {
        output.WriteRawTag(144, 1);
        output.WriteInt32(Version);
      }
      if (questCancelInfo_ != null) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(QuestCancelInfo);
      }
      if (QuestDependParentId != 0) {
        output.WriteRawTag(160, 1);
        output.WriteInt32(QuestDependParentId);
      }
      if (parentQuestInfo_ != null) {
        output.WriteRawTag(170, 1);
        output.WriteMessage(ParentQuestInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QuestId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestId);
      }
      if (QuestType != global::MainServer.QuestType.Once) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) QuestType);
      }
      if (QuestLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestLevel);
      }
      if (QuestStatus != global::MainServer.QuestStatus.Close) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) QuestStatus);
      }
      if (questUnlockInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(QuestUnlockInfo);
      }
      if (linearQuests_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(LinearQuests);
      }
      if (SingleQuest != false) {
        size += 1 + 1;
      }
      if (currentQuests_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(CurrentQuests);
      }
      if (questStrict_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(QuestStrict);
      }
      if (questRewardInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(QuestRewardInfo);
      }
      if (QuestStartTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(QuestStartTime);
      }
      if (QuestEndTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(QuestEndTime);
      }
      if (QuestRepeatLimit != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestRepeatLimit);
      }
      if (QuestRepeatInterval != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestRepeatInterval);
      }
      if (questBroadcast_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(QuestBroadcast);
      }
      if (questCompleteInfo_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(QuestCompleteInfo);
      }
      if (QuestRepeatLimitTime != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(QuestRepeatLimitTime);
      }
      if (Version != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(Version);
      }
      if (questCancelInfo_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(QuestCancelInfo);
      }
      if (QuestDependParentId != 0) {
        size += 2 + pb::CodedOutputStream.ComputeInt32Size(QuestDependParentId);
      }
      if (parentQuestInfo_ != null) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(ParentQuestInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestInfo other) {
      if (other == null) {
        return;
      }
      if (other.QuestId != 0) {
        QuestId = other.QuestId;
      }
      if (other.QuestType != global::MainServer.QuestType.Once) {
        QuestType = other.QuestType;
      }
      if (other.QuestLevel != 0) {
        QuestLevel = other.QuestLevel;
      }
      if (other.QuestStatus != global::MainServer.QuestStatus.Close) {
        QuestStatus = other.QuestStatus;
      }
      if (other.questUnlockInfo_ != null) {
        if (questUnlockInfo_ == null) {
          QuestUnlockInfo = new global::MainServer.QuestUnlockInfo();
        }
        QuestUnlockInfo.MergeFrom(other.QuestUnlockInfo);
      }
      if (other.linearQuests_ != null) {
        if (linearQuests_ == null) {
          LinearQuests = new global::MainServer.QuestListInfo();
        }
        LinearQuests.MergeFrom(other.LinearQuests);
      }
      if (other.SingleQuest != false) {
        SingleQuest = other.SingleQuest;
      }
      if (other.currentQuests_ != null) {
        if (currentQuests_ == null) {
          CurrentQuests = new global::MainServer.QuestListInfo();
        }
        CurrentQuests.MergeFrom(other.CurrentQuests);
      }
      if (other.questStrict_ != null) {
        if (questStrict_ == null) {
          QuestStrict = new global::MainServer.QuestStrictInfo();
        }
        QuestStrict.MergeFrom(other.QuestStrict);
      }
      if (other.questRewardInfo_ != null) {
        if (questRewardInfo_ == null) {
          QuestRewardInfo = new global::MainServer.QuestRewardInfo();
        }
        QuestRewardInfo.MergeFrom(other.QuestRewardInfo);
      }
      if (other.QuestStartTime != 0L) {
        QuestStartTime = other.QuestStartTime;
      }
      if (other.QuestEndTime != 0L) {
        QuestEndTime = other.QuestEndTime;
      }
      if (other.QuestRepeatLimit != 0) {
        QuestRepeatLimit = other.QuestRepeatLimit;
      }
      if (other.QuestRepeatInterval != 0) {
        QuestRepeatInterval = other.QuestRepeatInterval;
      }
      if (other.questBroadcast_ != null) {
        if (questBroadcast_ == null) {
          QuestBroadcast = new global::MainServer.QuestBroadcastInfo();
        }
        QuestBroadcast.MergeFrom(other.QuestBroadcast);
      }
      if (other.questCompleteInfo_ != null) {
        if (questCompleteInfo_ == null) {
          QuestCompleteInfo = new global::MainServer.QuestCompleteInfo();
        }
        QuestCompleteInfo.MergeFrom(other.QuestCompleteInfo);
      }
      if (other.QuestRepeatLimitTime != 0) {
        QuestRepeatLimitTime = other.QuestRepeatLimitTime;
      }
      if (other.Version != 0) {
        Version = other.Version;
      }
      if (other.questCancelInfo_ != null) {
        if (questCancelInfo_ == null) {
          QuestCancelInfo = new global::MainServer.QuestCancelInfo();
        }
        QuestCancelInfo.MergeFrom(other.QuestCancelInfo);
      }
      if (other.QuestDependParentId != 0) {
        QuestDependParentId = other.QuestDependParentId;
      }
      if (other.parentQuestInfo_ != null) {
        if (parentQuestInfo_ == null) {
          ParentQuestInfo = new global::MainServer.QuestInfo();
        }
        ParentQuestInfo.MergeFrom(other.ParentQuestInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestId = input.ReadInt32();
            break;
          }
          case 16: {
            QuestType = (global::MainServer.QuestType) input.ReadEnum();
            break;
          }
          case 24: {
            QuestLevel = input.ReadInt32();
            break;
          }
          case 32: {
            QuestStatus = (global::MainServer.QuestStatus) input.ReadEnum();
            break;
          }
          case 42: {
            if (questUnlockInfo_ == null) {
              QuestUnlockInfo = new global::MainServer.QuestUnlockInfo();
            }
            input.ReadMessage(QuestUnlockInfo);
            break;
          }
          case 50: {
            if (linearQuests_ == null) {
              LinearQuests = new global::MainServer.QuestListInfo();
            }
            input.ReadMessage(LinearQuests);
            break;
          }
          case 56: {
            SingleQuest = input.ReadBool();
            break;
          }
          case 66: {
            if (currentQuests_ == null) {
              CurrentQuests = new global::MainServer.QuestListInfo();
            }
            input.ReadMessage(CurrentQuests);
            break;
          }
          case 74: {
            if (questStrict_ == null) {
              QuestStrict = new global::MainServer.QuestStrictInfo();
            }
            input.ReadMessage(QuestStrict);
            break;
          }
          case 82: {
            if (questRewardInfo_ == null) {
              QuestRewardInfo = new global::MainServer.QuestRewardInfo();
            }
            input.ReadMessage(QuestRewardInfo);
            break;
          }
          case 88: {
            QuestStartTime = input.ReadInt64();
            break;
          }
          case 96: {
            QuestEndTime = input.ReadInt64();
            break;
          }
          case 104: {
            QuestRepeatLimit = input.ReadInt32();
            break;
          }
          case 112: {
            QuestRepeatInterval = input.ReadInt32();
            break;
          }
          case 122: {
            if (questBroadcast_ == null) {
              QuestBroadcast = new global::MainServer.QuestBroadcastInfo();
            }
            input.ReadMessage(QuestBroadcast);
            break;
          }
          case 130: {
            if (questCompleteInfo_ == null) {
              QuestCompleteInfo = new global::MainServer.QuestCompleteInfo();
            }
            input.ReadMessage(QuestCompleteInfo);
            break;
          }
          case 136: {
            QuestRepeatLimitTime = input.ReadInt32();
            break;
          }
          case 144: {
            Version = input.ReadInt32();
            break;
          }
          case 154: {
            if (questCancelInfo_ == null) {
              QuestCancelInfo = new global::MainServer.QuestCancelInfo();
            }
            input.ReadMessage(QuestCancelInfo);
            break;
          }
          case 160: {
            QuestDependParentId = input.ReadInt32();
            break;
          }
          case 170: {
            if (parentQuestInfo_ == null) {
              ParentQuestInfo = new global::MainServer.QuestInfo();
            }
            input.ReadMessage(ParentQuestInfo);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestId = input.ReadInt32();
            break;
          }
          case 16: {
            QuestType = (global::MainServer.QuestType) input.ReadEnum();
            break;
          }
          case 24: {
            QuestLevel = input.ReadInt32();
            break;
          }
          case 32: {
            QuestStatus = (global::MainServer.QuestStatus) input.ReadEnum();
            break;
          }
          case 42: {
            if (questUnlockInfo_ == null) {
              QuestUnlockInfo = new global::MainServer.QuestUnlockInfo();
            }
            input.ReadMessage(QuestUnlockInfo);
            break;
          }
          case 50: {
            if (linearQuests_ == null) {
              LinearQuests = new global::MainServer.QuestListInfo();
            }
            input.ReadMessage(LinearQuests);
            break;
          }
          case 56: {
            SingleQuest = input.ReadBool();
            break;
          }
          case 66: {
            if (currentQuests_ == null) {
              CurrentQuests = new global::MainServer.QuestListInfo();
            }
            input.ReadMessage(CurrentQuests);
            break;
          }
          case 74: {
            if (questStrict_ == null) {
              QuestStrict = new global::MainServer.QuestStrictInfo();
            }
            input.ReadMessage(QuestStrict);
            break;
          }
          case 82: {
            if (questRewardInfo_ == null) {
              QuestRewardInfo = new global::MainServer.QuestRewardInfo();
            }
            input.ReadMessage(QuestRewardInfo);
            break;
          }
          case 88: {
            QuestStartTime = input.ReadInt64();
            break;
          }
          case 96: {
            QuestEndTime = input.ReadInt64();
            break;
          }
          case 104: {
            QuestRepeatLimit = input.ReadInt32();
            break;
          }
          case 112: {
            QuestRepeatInterval = input.ReadInt32();
            break;
          }
          case 122: {
            if (questBroadcast_ == null) {
              QuestBroadcast = new global::MainServer.QuestBroadcastInfo();
            }
            input.ReadMessage(QuestBroadcast);
            break;
          }
          case 130: {
            if (questCompleteInfo_ == null) {
              QuestCompleteInfo = new global::MainServer.QuestCompleteInfo();
            }
            input.ReadMessage(QuestCompleteInfo);
            break;
          }
          case 136: {
            QuestRepeatLimitTime = input.ReadInt32();
            break;
          }
          case 144: {
            Version = input.ReadInt32();
            break;
          }
          case 154: {
            if (questCancelInfo_ == null) {
              QuestCancelInfo = new global::MainServer.QuestCancelInfo();
            }
            input.ReadMessage(QuestCancelInfo);
            break;
          }
          case 160: {
            QuestDependParentId = input.ReadInt32();
            break;
          }
          case 170: {
            if (parentQuestInfo_ == null) {
              ParentQuestInfo = new global::MainServer.QuestInfo();
            }
            input.ReadMessage(ParentQuestInfo);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestCancelInfo : pb::IMessage<QuestCancelInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestCancelInfo> _parser = new pb::MessageParser<QuestCancelInfo>(() => new QuestCancelInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestCancelInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestCancelInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestCancelInfo(QuestCancelInfo other) : this() {
      cancelInterval_ = other.cancelInterval_;
      cancelAcceptInterval_ = other.cancelAcceptInterval_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestCancelInfo Clone() {
      return new QuestCancelInfo(this);
    }

    /// <summary>Field number for the "cancel_interval" field.</summary>
    public const int CancelIntervalFieldNumber = 1;
    private int cancelInterval_;
    /// <summary>
    ///多久之后这个任务可以取消
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CancelInterval {
      get { return cancelInterval_; }
      set {
        cancelInterval_ = value;
      }
    }

    /// <summary>Field number for the "cancel_accept_interval" field.</summary>
    public const int CancelAcceptIntervalFieldNumber = 2;
    private int cancelAcceptInterval_;
    /// <summary>
    /// 取消之后过多久才能再次接受任务的间隔
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CancelAcceptInterval {
      get { return cancelAcceptInterval_; }
      set {
        cancelAcceptInterval_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestCancelInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestCancelInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (CancelInterval != other.CancelInterval) return false;
      if (CancelAcceptInterval != other.CancelAcceptInterval) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (CancelInterval != 0) hash ^= CancelInterval.GetHashCode();
      if (CancelAcceptInterval != 0) hash ^= CancelAcceptInterval.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (CancelInterval != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(CancelInterval);
      }
      if (CancelAcceptInterval != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(CancelAcceptInterval);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (CancelInterval != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(CancelInterval);
      }
      if (CancelAcceptInterval != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(CancelAcceptInterval);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (CancelInterval != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(CancelInterval);
      }
      if (CancelAcceptInterval != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(CancelAcceptInterval);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestCancelInfo other) {
      if (other == null) {
        return;
      }
      if (other.CancelInterval != 0) {
        CancelInterval = other.CancelInterval;
      }
      if (other.CancelAcceptInterval != 0) {
        CancelAcceptInterval = other.CancelAcceptInterval;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            CancelInterval = input.ReadInt32();
            break;
          }
          case 16: {
            CancelAcceptInterval = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            CancelInterval = input.ReadInt32();
            break;
          }
          case 16: {
            CancelAcceptInterval = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestListInfo : pb::IMessage<QuestListInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestListInfo> _parser = new pb::MessageParser<QuestListInfo>(() => new QuestListInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestListInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestListInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestListInfo(QuestListInfo other) : this() {
      hasList_ = other.hasList_;
      questIds_ = other.questIds_.Clone();
      questInfos_ = other.questInfos_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestListInfo Clone() {
      return new QuestListInfo(this);
    }

    /// <summary>Field number for the "has_list" field.</summary>
    public const int HasListFieldNumber = 1;
    private bool hasList_;
    /// <summary>
    /// 是否有线性任务
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasList {
      get { return hasList_; }
      set {
        hasList_ = value;
      }
    }

    /// <summary>Field number for the "quest_ids" field.</summary>
    public const int QuestIdsFieldNumber = 2;
    private static readonly pb::FieldCodec<int> _repeated_questIds_codec
        = pb::FieldCodec.ForInt32(18);
    private readonly pbc::RepeatedField<int> questIds_ = new pbc::RepeatedField<int>();
    /// <summary>
    /// 任务id列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<int> QuestIds {
      get { return questIds_; }
    }

    /// <summary>Field number for the "quest_infos" field.</summary>
    public const int QuestInfosFieldNumber = 3;
    private static readonly pb::FieldCodec<global::MainServer.QuestInfo> _repeated_questInfos_codec
        = pb::FieldCodec.ForMessage(26, global::MainServer.QuestInfo.Parser);
    private readonly pbc::RepeatedField<global::MainServer.QuestInfo> questInfos_ = new pbc::RepeatedField<global::MainServer.QuestInfo>();
    /// <summary>
    /// 任务信息列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::MainServer.QuestInfo> QuestInfos {
      get { return questInfos_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestListInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestListInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (HasList != other.HasList) return false;
      if(!questIds_.Equals(other.questIds_)) return false;
      if(!questInfos_.Equals(other.questInfos_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasList != false) hash ^= HasList.GetHashCode();
      hash ^= questIds_.GetHashCode();
      hash ^= questInfos_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasList != false) {
        output.WriteRawTag(8);
        output.WriteBool(HasList);
      }
      questIds_.WriteTo(output, _repeated_questIds_codec);
      questInfos_.WriteTo(output, _repeated_questInfos_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasList != false) {
        output.WriteRawTag(8);
        output.WriteBool(HasList);
      }
      questIds_.WriteTo(ref output, _repeated_questIds_codec);
      questInfos_.WriteTo(ref output, _repeated_questInfos_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasList != false) {
        size += 1 + 1;
      }
      size += questIds_.CalculateSize(_repeated_questIds_codec);
      size += questInfos_.CalculateSize(_repeated_questInfos_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestListInfo other) {
      if (other == null) {
        return;
      }
      if (other.HasList != false) {
        HasList = other.HasList;
      }
      questIds_.Add(other.questIds_);
      questInfos_.Add(other.questInfos_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            HasList = input.ReadBool();
            break;
          }
          case 18:
          case 16: {
            questIds_.AddEntriesFrom(input, _repeated_questIds_codec);
            break;
          }
          case 26: {
            questInfos_.AddEntriesFrom(input, _repeated_questInfos_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            HasList = input.ReadBool();
            break;
          }
          case 18:
          case 16: {
            questIds_.AddEntriesFrom(ref input, _repeated_questIds_codec);
            break;
          }
          case 26: {
            questInfos_.AddEntriesFrom(ref input, _repeated_questInfos_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestStrictInfo : pb::IMessage<QuestStrictInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestStrictInfo> _parser = new pb::MessageParser<QuestStrictInfo>(() => new QuestStrictInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestStrictInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestStrictInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestStrictInfo(QuestStrictInfo other) : this() {
      questStrictId_ = other.questStrictId_;
      questStricts_ = other.questStricts_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestStrictInfo Clone() {
      return new QuestStrictInfo(this);
    }

    /// <summary>Field number for the "quest_strict_id" field.</summary>
    public const int QuestStrictIdFieldNumber = 1;
    private int questStrictId_;
    /// <summary>
    /// 限制id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestStrictId {
      get { return questStrictId_; }
      set {
        questStrictId_ = value;
      }
    }

    /// <summary>Field number for the "quest_stricts" field.</summary>
    public const int QuestStrictsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::MainServer.TrainerStrict> _repeated_questStricts_codec
        = pb::FieldCodec.ForMessage(18, global::MainServer.TrainerStrict.Parser);
    private readonly pbc::RepeatedField<global::MainServer.TrainerStrict> questStricts_ = new pbc::RepeatedField<global::MainServer.TrainerStrict>();
    /// <summary>
    /// 限制类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::MainServer.TrainerStrict> QuestStricts {
      get { return questStricts_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestStrictInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestStrictInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestStrictId != other.QuestStrictId) return false;
      if(!questStricts_.Equals(other.questStricts_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestStrictId != 0) hash ^= QuestStrictId.GetHashCode();
      hash ^= questStricts_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestStrictId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestStrictId);
      }
      questStricts_.WriteTo(output, _repeated_questStricts_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestStrictId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestStrictId);
      }
      questStricts_.WriteTo(ref output, _repeated_questStricts_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QuestStrictId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestStrictId);
      }
      size += questStricts_.CalculateSize(_repeated_questStricts_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestStrictInfo other) {
      if (other == null) {
        return;
      }
      if (other.QuestStrictId != 0) {
        QuestStrictId = other.QuestStrictId;
      }
      questStricts_.Add(other.questStricts_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestStrictId = input.ReadInt32();
            break;
          }
          case 18: {
            questStricts_.AddEntriesFrom(input, _repeated_questStricts_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestStrictId = input.ReadInt32();
            break;
          }
          case 18: {
            questStricts_.AddEntriesFrom(ref input, _repeated_questStricts_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestUnlockInfo : pb::IMessage<QuestUnlockInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestUnlockInfo> _parser = new pb::MessageParser<QuestUnlockInfo>(() => new QuestUnlockInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestUnlockInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestUnlockInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestUnlockInfo(QuestUnlockInfo other) : this() {
      questUnlockId_ = other.questUnlockId_;
      questUnlockConditions_ = other.questUnlockConditions_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestUnlockInfo Clone() {
      return new QuestUnlockInfo(this);
    }

    /// <summary>Field number for the "quest_unlock_id" field.</summary>
    public const int QuestUnlockIdFieldNumber = 1;
    private int questUnlockId_;
    /// <summary>
    /// 解锁id 配置表中配置
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestUnlockId {
      get { return questUnlockId_; }
      set {
        questUnlockId_ = value;
      }
    }

    /// <summary>Field number for the "quest_unlock_conditions" field.</summary>
    public const int QuestUnlockConditionsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::MainServer.QuestUnlockConditionInfo> _repeated_questUnlockConditions_codec
        = pb::FieldCodec.ForMessage(18, global::MainServer.QuestUnlockConditionInfo.Parser);
    private readonly pbc::RepeatedField<global::MainServer.QuestUnlockConditionInfo> questUnlockConditions_ = new pbc::RepeatedField<global::MainServer.QuestUnlockConditionInfo>();
    /// <summary>
    /// 解锁条件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::MainServer.QuestUnlockConditionInfo> QuestUnlockConditions {
      get { return questUnlockConditions_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestUnlockInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestUnlockInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestUnlockId != other.QuestUnlockId) return false;
      if(!questUnlockConditions_.Equals(other.questUnlockConditions_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestUnlockId != 0) hash ^= QuestUnlockId.GetHashCode();
      hash ^= questUnlockConditions_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestUnlockId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestUnlockId);
      }
      questUnlockConditions_.WriteTo(output, _repeated_questUnlockConditions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestUnlockId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestUnlockId);
      }
      questUnlockConditions_.WriteTo(ref output, _repeated_questUnlockConditions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QuestUnlockId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestUnlockId);
      }
      size += questUnlockConditions_.CalculateSize(_repeated_questUnlockConditions_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestUnlockInfo other) {
      if (other == null) {
        return;
      }
      if (other.QuestUnlockId != 0) {
        QuestUnlockId = other.QuestUnlockId;
      }
      questUnlockConditions_.Add(other.questUnlockConditions_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestUnlockId = input.ReadInt32();
            break;
          }
          case 18: {
            questUnlockConditions_.AddEntriesFrom(input, _repeated_questUnlockConditions_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestUnlockId = input.ReadInt32();
            break;
          }
          case 18: {
            questUnlockConditions_.AddEntriesFrom(ref input, _repeated_questUnlockConditions_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestUnlockConditionInfo : pb::IMessage<QuestUnlockConditionInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestUnlockConditionInfo> _parser = new pb::MessageParser<QuestUnlockConditionInfo>(() => new QuestUnlockConditionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestUnlockConditionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestUnlockConditionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestUnlockConditionInfo(QuestUnlockConditionInfo other) : this() {
      questUnlockType_ = other.questUnlockType_;
      questCondition_ = other.questCondition_ != null ? other.questCondition_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestUnlockConditionInfo Clone() {
      return new QuestUnlockConditionInfo(this);
    }

    /// <summary>Field number for the "quest_unlock_type" field.</summary>
    public const int QuestUnlockTypeFieldNumber = 1;
    private global::MainServer.QuestUnlockType questUnlockType_ = global::MainServer.QuestUnlockType.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestUnlockType QuestUnlockType {
      get { return questUnlockType_; }
      set {
        questUnlockType_ = value;
      }
    }

    /// <summary>Field number for the "quest_condition" field.</summary>
    public const int QuestConditionFieldNumber = 2;
    private global::MainServer.QuestConditionInfo questCondition_;
    /// <summary>
    /// 解锁条件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestConditionInfo QuestCondition {
      get { return questCondition_; }
      set {
        questCondition_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestUnlockConditionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestUnlockConditionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestUnlockType != other.QuestUnlockType) return false;
      if (!object.Equals(QuestCondition, other.QuestCondition)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestUnlockType != global::MainServer.QuestUnlockType.None) hash ^= QuestUnlockType.GetHashCode();
      if (questCondition_ != null) hash ^= QuestCondition.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestUnlockType != global::MainServer.QuestUnlockType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) QuestUnlockType);
      }
      if (questCondition_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(QuestCondition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestUnlockType != global::MainServer.QuestUnlockType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) QuestUnlockType);
      }
      if (questCondition_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(QuestCondition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QuestUnlockType != global::MainServer.QuestUnlockType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) QuestUnlockType);
      }
      if (questCondition_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(QuestCondition);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestUnlockConditionInfo other) {
      if (other == null) {
        return;
      }
      if (other.QuestUnlockType != global::MainServer.QuestUnlockType.None) {
        QuestUnlockType = other.QuestUnlockType;
      }
      if (other.questCondition_ != null) {
        if (questCondition_ == null) {
          QuestCondition = new global::MainServer.QuestConditionInfo();
        }
        QuestCondition.MergeFrom(other.QuestCondition);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestUnlockType = (global::MainServer.QuestUnlockType) input.ReadEnum();
            break;
          }
          case 18: {
            if (questCondition_ == null) {
              QuestCondition = new global::MainServer.QuestConditionInfo();
            }
            input.ReadMessage(QuestCondition);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestUnlockType = (global::MainServer.QuestUnlockType) input.ReadEnum();
            break;
          }
          case 18: {
            if (questCondition_ == null) {
              QuestCondition = new global::MainServer.QuestConditionInfo();
            }
            input.ReadMessage(QuestCondition);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///包括解锁和完成条件
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestConditionInfo : pb::IMessage<QuestConditionInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestConditionInfo> _parser = new pb::MessageParser<QuestConditionInfo>(() => new QuestConditionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestConditionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestConditionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestConditionInfo(QuestConditionInfo other) : this() {
      conditionNameId_ = other.conditionNameId_;
      conditionCount_ = other.conditionCount_;
      jsonValue_ = other.jsonValue_;
      timeLimit_ = other.timeLimit_;
      isUsed_ = other.isUsed_;
      isAdd_ = other.isAdd_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestConditionInfo Clone() {
      return new QuestConditionInfo(this);
    }

    /// <summary>Field number for the "condition_name_id" field.</summary>
    public const int ConditionNameIdFieldNumber = 1;
    private string conditionNameId_ = "";
    /// <summary>
    /// 条件名称id(可能是poke的nameid或者item的nameid等等等)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ConditionNameId {
      get { return conditionNameId_; }
      set {
        conditionNameId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "condition_count" field.</summary>
    public const int ConditionCountFieldNumber = 2;
    private int conditionCount_;
    /// <summary>
    /// 条件数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ConditionCount {
      get { return conditionCount_; }
      set {
        conditionCount_ = value;
      }
    }

    /// <summary>Field number for the "json_value" field.</summary>
    public const int JsonValueFieldNumber = 3;
    private string jsonValue_ = "";
    /// <summary>
    /// json值 (一些特别的配置（）)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string JsonValue {
      get { return jsonValue_; }
      set {
        jsonValue_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "time_limit" field.</summary>
    public const int TimeLimitFieldNumber = 4;
    private int timeLimit_;
    /// <summary>
    /// 时间限制(秒) //比如任务是要在这个时间范围内的，超出后就无效了
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TimeLimit {
      get { return timeLimit_; }
      set {
        timeLimit_ = value;
      }
    }

    /// <summary>Field number for the "is_used" field.</summary>
    public const int IsUsedFieldNumber = 5;
    private bool isUsed_;
    /// <summary>
    /// 是否使用
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsUsed {
      get { return isUsed_; }
      set {
        isUsed_ = value;
      }
    }

    /// <summary>Field number for the "is_add" field.</summary>
    public const int IsAddFieldNumber = 6;
    private bool isAdd_;
    /// <summary>
    /// 是否为增加条件 (是否给予道具或poke，比如与某poke并肩作战)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsAdd {
      get { return isAdd_; }
      set {
        isAdd_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestConditionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestConditionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ConditionNameId != other.ConditionNameId) return false;
      if (ConditionCount != other.ConditionCount) return false;
      if (JsonValue != other.JsonValue) return false;
      if (TimeLimit != other.TimeLimit) return false;
      if (IsUsed != other.IsUsed) return false;
      if (IsAdd != other.IsAdd) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ConditionNameId.Length != 0) hash ^= ConditionNameId.GetHashCode();
      if (ConditionCount != 0) hash ^= ConditionCount.GetHashCode();
      if (JsonValue.Length != 0) hash ^= JsonValue.GetHashCode();
      if (TimeLimit != 0) hash ^= TimeLimit.GetHashCode();
      if (IsUsed != false) hash ^= IsUsed.GetHashCode();
      if (IsAdd != false) hash ^= IsAdd.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ConditionNameId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ConditionNameId);
      }
      if (ConditionCount != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ConditionCount);
      }
      if (JsonValue.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(JsonValue);
      }
      if (TimeLimit != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(TimeLimit);
      }
      if (IsUsed != false) {
        output.WriteRawTag(40);
        output.WriteBool(IsUsed);
      }
      if (IsAdd != false) {
        output.WriteRawTag(48);
        output.WriteBool(IsAdd);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ConditionNameId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ConditionNameId);
      }
      if (ConditionCount != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ConditionCount);
      }
      if (JsonValue.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(JsonValue);
      }
      if (TimeLimit != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(TimeLimit);
      }
      if (IsUsed != false) {
        output.WriteRawTag(40);
        output.WriteBool(IsUsed);
      }
      if (IsAdd != false) {
        output.WriteRawTag(48);
        output.WriteBool(IsAdd);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ConditionNameId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ConditionNameId);
      }
      if (ConditionCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ConditionCount);
      }
      if (JsonValue.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(JsonValue);
      }
      if (TimeLimit != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TimeLimit);
      }
      if (IsUsed != false) {
        size += 1 + 1;
      }
      if (IsAdd != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestConditionInfo other) {
      if (other == null) {
        return;
      }
      if (other.ConditionNameId.Length != 0) {
        ConditionNameId = other.ConditionNameId;
      }
      if (other.ConditionCount != 0) {
        ConditionCount = other.ConditionCount;
      }
      if (other.JsonValue.Length != 0) {
        JsonValue = other.JsonValue;
      }
      if (other.TimeLimit != 0) {
        TimeLimit = other.TimeLimit;
      }
      if (other.IsUsed != false) {
        IsUsed = other.IsUsed;
      }
      if (other.IsAdd != false) {
        IsAdd = other.IsAdd;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ConditionNameId = input.ReadString();
            break;
          }
          case 16: {
            ConditionCount = input.ReadInt32();
            break;
          }
          case 26: {
            JsonValue = input.ReadString();
            break;
          }
          case 32: {
            TimeLimit = input.ReadInt32();
            break;
          }
          case 40: {
            IsUsed = input.ReadBool();
            break;
          }
          case 48: {
            IsAdd = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ConditionNameId = input.ReadString();
            break;
          }
          case 16: {
            ConditionCount = input.ReadInt32();
            break;
          }
          case 26: {
            JsonValue = input.ReadString();
            break;
          }
          case 32: {
            TimeLimit = input.ReadInt32();
            break;
          }
          case 40: {
            IsUsed = input.ReadBool();
            break;
          }
          case 48: {
            IsAdd = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestCompleteConditionInfo : pb::IMessage<QuestCompleteConditionInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestCompleteConditionInfo> _parser = new pb::MessageParser<QuestCompleteConditionInfo>(() => new QuestCompleteConditionInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestCompleteConditionInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestCompleteConditionInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestCompleteConditionInfo(QuestCompleteConditionInfo other) : this() {
      questCompleteType_ = other.questCompleteType_;
      questCondition_ = other.questCondition_ != null ? other.questCondition_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestCompleteConditionInfo Clone() {
      return new QuestCompleteConditionInfo(this);
    }

    /// <summary>Field number for the "quest_complete_type" field.</summary>
    public const int QuestCompleteTypeFieldNumber = 1;
    private global::MainServer.QuestCompleteType questCompleteType_ = global::MainServer.QuestCompleteType.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestCompleteType QuestCompleteType {
      get { return questCompleteType_; }
      set {
        questCompleteType_ = value;
      }
    }

    /// <summary>Field number for the "quest_condition" field.</summary>
    public const int QuestConditionFieldNumber = 2;
    private global::MainServer.QuestConditionInfo questCondition_;
    /// <summary>
    /// 解锁条件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestConditionInfo QuestCondition {
      get { return questCondition_; }
      set {
        questCondition_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestCompleteConditionInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestCompleteConditionInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestCompleteType != other.QuestCompleteType) return false;
      if (!object.Equals(QuestCondition, other.QuestCondition)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestCompleteType != global::MainServer.QuestCompleteType.None) hash ^= QuestCompleteType.GetHashCode();
      if (questCondition_ != null) hash ^= QuestCondition.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestCompleteType != global::MainServer.QuestCompleteType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) QuestCompleteType);
      }
      if (questCondition_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(QuestCondition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestCompleteType != global::MainServer.QuestCompleteType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) QuestCompleteType);
      }
      if (questCondition_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(QuestCondition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QuestCompleteType != global::MainServer.QuestCompleteType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) QuestCompleteType);
      }
      if (questCondition_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(QuestCondition);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestCompleteConditionInfo other) {
      if (other == null) {
        return;
      }
      if (other.QuestCompleteType != global::MainServer.QuestCompleteType.None) {
        QuestCompleteType = other.QuestCompleteType;
      }
      if (other.questCondition_ != null) {
        if (questCondition_ == null) {
          QuestCondition = new global::MainServer.QuestConditionInfo();
        }
        QuestCondition.MergeFrom(other.QuestCondition);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestCompleteType = (global::MainServer.QuestCompleteType) input.ReadEnum();
            break;
          }
          case 18: {
            if (questCondition_ == null) {
              QuestCondition = new global::MainServer.QuestConditionInfo();
            }
            input.ReadMessage(QuestCondition);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestCompleteType = (global::MainServer.QuestCompleteType) input.ReadEnum();
            break;
          }
          case 18: {
            if (questCondition_ == null) {
              QuestCondition = new global::MainServer.QuestConditionInfo();
            }
            input.ReadMessage(QuestCondition);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestCompleteInfo : pb::IMessage<QuestCompleteInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestCompleteInfo> _parser = new pb::MessageParser<QuestCompleteInfo>(() => new QuestCompleteInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestCompleteInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestCompleteInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestCompleteInfo(QuestCompleteInfo other) : this() {
      questCompleteId_ = other.questCompleteId_;
      questCompleteConditions_ = other.questCompleteConditions_.Clone();
      questCompleteReportName_ = other.questCompleteReportName_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestCompleteInfo Clone() {
      return new QuestCompleteInfo(this);
    }

    /// <summary>Field number for the "quest_complete_id" field.</summary>
    public const int QuestCompleteIdFieldNumber = 1;
    private int questCompleteId_;
    /// <summary>
    /// 完成id 配置表中配置
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestCompleteId {
      get { return questCompleteId_; }
      set {
        questCompleteId_ = value;
      }
    }

    /// <summary>Field number for the "quest_complete_conditions" field.</summary>
    public const int QuestCompleteConditionsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::MainServer.QuestCompleteConditionInfo> _repeated_questCompleteConditions_codec
        = pb::FieldCodec.ForMessage(18, global::MainServer.QuestCompleteConditionInfo.Parser);
    private readonly pbc::RepeatedField<global::MainServer.QuestCompleteConditionInfo> questCompleteConditions_ = new pbc::RepeatedField<global::MainServer.QuestCompleteConditionInfo>();
    /// <summary>
    /// 完成条件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::MainServer.QuestCompleteConditionInfo> QuestCompleteConditions {
      get { return questCompleteConditions_; }
    }

    /// <summary>Field number for the "quest_complete_report_name" field.</summary>
    public const int QuestCompleteReportNameFieldNumber = 3;
    private string questCompleteReportName_ = "";
    /// <summary>
    ///完成后汇报的目标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string QuestCompleteReportName {
      get { return questCompleteReportName_; }
      set {
        questCompleteReportName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestCompleteInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestCompleteInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestCompleteId != other.QuestCompleteId) return false;
      if(!questCompleteConditions_.Equals(other.questCompleteConditions_)) return false;
      if (QuestCompleteReportName != other.QuestCompleteReportName) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestCompleteId != 0) hash ^= QuestCompleteId.GetHashCode();
      hash ^= questCompleteConditions_.GetHashCode();
      if (QuestCompleteReportName.Length != 0) hash ^= QuestCompleteReportName.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestCompleteId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestCompleteId);
      }
      questCompleteConditions_.WriteTo(output, _repeated_questCompleteConditions_codec);
      if (QuestCompleteReportName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(QuestCompleteReportName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestCompleteId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestCompleteId);
      }
      questCompleteConditions_.WriteTo(ref output, _repeated_questCompleteConditions_codec);
      if (QuestCompleteReportName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(QuestCompleteReportName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QuestCompleteId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestCompleteId);
      }
      size += questCompleteConditions_.CalculateSize(_repeated_questCompleteConditions_codec);
      if (QuestCompleteReportName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(QuestCompleteReportName);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestCompleteInfo other) {
      if (other == null) {
        return;
      }
      if (other.QuestCompleteId != 0) {
        QuestCompleteId = other.QuestCompleteId;
      }
      questCompleteConditions_.Add(other.questCompleteConditions_);
      if (other.QuestCompleteReportName.Length != 0) {
        QuestCompleteReportName = other.QuestCompleteReportName;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestCompleteId = input.ReadInt32();
            break;
          }
          case 18: {
            questCompleteConditions_.AddEntriesFrom(input, _repeated_questCompleteConditions_codec);
            break;
          }
          case 26: {
            QuestCompleteReportName = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestCompleteId = input.ReadInt32();
            break;
          }
          case 18: {
            questCompleteConditions_.AddEntriesFrom(ref input, _repeated_questCompleteConditions_codec);
            break;
          }
          case 26: {
            QuestCompleteReportName = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestRewardInfo : pb::IMessage<QuestRewardInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestRewardInfo> _parser = new pb::MessageParser<QuestRewardInfo>(() => new QuestRewardInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestRewardInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestRewardInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestRewardInfo(QuestRewardInfo other) : this() {
      questRewardId_ = other.questRewardId_;
      questRewards_ = other.questRewards_.Clone();
      randomCount_ = other.randomCount_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestRewardInfo Clone() {
      return new QuestRewardInfo(this);
    }

    /// <summary>Field number for the "quest_reward_id" field.</summary>
    public const int QuestRewardIdFieldNumber = 1;
    private int questRewardId_;
    /// <summary>
    /// 奖励id 可以去配置表中读取
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestRewardId {
      get { return questRewardId_; }
      set {
        questRewardId_ = value;
      }
    }

    /// <summary>Field number for the "quest_rewards" field.</summary>
    public const int QuestRewardsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::MainServer.QuestRewardValue> _repeated_questRewards_codec
        = pb::FieldCodec.ForMessage(18, global::MainServer.QuestRewardValue.Parser);
    private readonly pbc::RepeatedField<global::MainServer.QuestRewardValue> questRewards_ = new pbc::RepeatedField<global::MainServer.QuestRewardValue>();
    /// <summary>
    /// 奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::MainServer.QuestRewardValue> QuestRewards {
      get { return questRewards_; }
    }

    /// <summary>Field number for the "random_count" field.</summary>
    public const int RandomCountFieldNumber = 3;
    private int randomCount_;
    /// <summary>
    /// 随机数量 (0或者1)都是数量1
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int RandomCount {
      get { return randomCount_; }
      set {
        randomCount_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestRewardInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestRewardInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestRewardId != other.QuestRewardId) return false;
      if(!questRewards_.Equals(other.questRewards_)) return false;
      if (RandomCount != other.RandomCount) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestRewardId != 0) hash ^= QuestRewardId.GetHashCode();
      hash ^= questRewards_.GetHashCode();
      if (RandomCount != 0) hash ^= RandomCount.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestRewardId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestRewardId);
      }
      questRewards_.WriteTo(output, _repeated_questRewards_codec);
      if (RandomCount != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(RandomCount);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestRewardId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestRewardId);
      }
      questRewards_.WriteTo(ref output, _repeated_questRewards_codec);
      if (RandomCount != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(RandomCount);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QuestRewardId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestRewardId);
      }
      size += questRewards_.CalculateSize(_repeated_questRewards_codec);
      if (RandomCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RandomCount);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestRewardInfo other) {
      if (other == null) {
        return;
      }
      if (other.QuestRewardId != 0) {
        QuestRewardId = other.QuestRewardId;
      }
      questRewards_.Add(other.questRewards_);
      if (other.RandomCount != 0) {
        RandomCount = other.RandomCount;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestRewardId = input.ReadInt32();
            break;
          }
          case 18: {
            questRewards_.AddEntriesFrom(input, _repeated_questRewards_codec);
            break;
          }
          case 24: {
            RandomCount = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestRewardId = input.ReadInt32();
            break;
          }
          case 18: {
            questRewards_.AddEntriesFrom(ref input, _repeated_questRewards_codec);
            break;
          }
          case 24: {
            RandomCount = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestRewardValue : pb::IMessage<QuestRewardValue>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestRewardValue> _parser = new pb::MessageParser<QuestRewardValue>(() => new QuestRewardValue());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestRewardValue> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestRewardValue() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestRewardValue(QuestRewardValue other) : this() {
      questRewardType_ = other.questRewardType_;
      questRewardRate_ = other.questRewardRate_;
      questRewardCount_ = other.questRewardCount_;
      questRewardCountRate_ = other.questRewardCountRate_;
      dayWholeNetlocked_ = other.dayWholeNetlocked_;
      questRewardValue_ = other.questRewardValue_;
      questRewardBaseMoney_ = other.questRewardBaseMoney_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestRewardValue Clone() {
      return new QuestRewardValue(this);
    }

    /// <summary>Field number for the "quest_reward_type" field.</summary>
    public const int QuestRewardTypeFieldNumber = 1;
    private global::MainServer.QuestRewardType questRewardType_ = global::MainServer.QuestRewardType.None;
    /// <summary>
    /// 奖励类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestRewardType QuestRewardType {
      get { return questRewardType_; }
      set {
        questRewardType_ = value;
      }
    }

    /// <summary>Field number for the "quest_reward_rate" field.</summary>
    public const int QuestRewardRateFieldNumber = 2;
    private float questRewardRate_;
    /// <summary>
    /// 奖励概率
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float QuestRewardRate {
      get { return questRewardRate_; }
      set {
        questRewardRate_ = value;
      }
    }

    /// <summary>Field number for the "quest_reward_count" field.</summary>
    public const int QuestRewardCountFieldNumber = 3;
    private int questRewardCount_;
    /// <summary>
    /// 奖励数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestRewardCount {
      get { return questRewardCount_; }
      set {
        questRewardCount_ = value;
      }
    }

    /// <summary>Field number for the "quest_reward_count_rate" field.</summary>
    public const int QuestRewardCountRateFieldNumber = 4;
    private global::MainServer.QuestRewardValueCountRateType questRewardCountRate_ = global::MainServer.QuestRewardValueCountRateType.None;
    /// <summary>
    /// 奖励数量概率
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestRewardValueCountRateType QuestRewardCountRate {
      get { return questRewardCountRate_; }
      set {
        questRewardCountRate_ = value;
      }
    }

    /// <summary>Field number for the "day_whole_netlocked" field.</summary>
    public const int DayWholeNetlockedFieldNumber = 5;
    private int dayWholeNetlocked_;
    /// <summary>
    /// 一天全网锁定数量  //todo
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DayWholeNetlocked {
      get { return dayWholeNetlocked_; }
      set {
        dayWholeNetlocked_ = value;
      }
    }

    /// <summary>Field number for the "quest_reward_value" field.</summary>
    public const int QuestRewardValue_FieldNumber = 6;
    private string questRewardValue_ = "";
    /// <summary>
    /// 具体奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string QuestRewardValue_ {
      get { return questRewardValue_; }
      set {
        questRewardValue_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "quest_reward_base_money" field.</summary>
    public const int QuestRewardBaseMoneyFieldNumber = 7;
    private int questRewardBaseMoney_;
    /// <summary>
    /// 基础奖励金额
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestRewardBaseMoney {
      get { return questRewardBaseMoney_; }
      set {
        questRewardBaseMoney_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestRewardValue);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestRewardValue other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestRewardType != other.QuestRewardType) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(QuestRewardRate, other.QuestRewardRate)) return false;
      if (QuestRewardCount != other.QuestRewardCount) return false;
      if (QuestRewardCountRate != other.QuestRewardCountRate) return false;
      if (DayWholeNetlocked != other.DayWholeNetlocked) return false;
      if (QuestRewardValue_ != other.QuestRewardValue_) return false;
      if (QuestRewardBaseMoney != other.QuestRewardBaseMoney) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestRewardType != global::MainServer.QuestRewardType.None) hash ^= QuestRewardType.GetHashCode();
      if (QuestRewardRate != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(QuestRewardRate);
      if (QuestRewardCount != 0) hash ^= QuestRewardCount.GetHashCode();
      if (QuestRewardCountRate != global::MainServer.QuestRewardValueCountRateType.None) hash ^= QuestRewardCountRate.GetHashCode();
      if (DayWholeNetlocked != 0) hash ^= DayWholeNetlocked.GetHashCode();
      if (QuestRewardValue_.Length != 0) hash ^= QuestRewardValue_.GetHashCode();
      if (QuestRewardBaseMoney != 0) hash ^= QuestRewardBaseMoney.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestRewardType != global::MainServer.QuestRewardType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) QuestRewardType);
      }
      if (QuestRewardRate != 0F) {
        output.WriteRawTag(21);
        output.WriteFloat(QuestRewardRate);
      }
      if (QuestRewardCount != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(QuestRewardCount);
      }
      if (QuestRewardCountRate != global::MainServer.QuestRewardValueCountRateType.None) {
        output.WriteRawTag(32);
        output.WriteEnum((int) QuestRewardCountRate);
      }
      if (DayWholeNetlocked != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(DayWholeNetlocked);
      }
      if (QuestRewardValue_.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(QuestRewardValue_);
      }
      if (QuestRewardBaseMoney != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(QuestRewardBaseMoney);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestRewardType != global::MainServer.QuestRewardType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) QuestRewardType);
      }
      if (QuestRewardRate != 0F) {
        output.WriteRawTag(21);
        output.WriteFloat(QuestRewardRate);
      }
      if (QuestRewardCount != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(QuestRewardCount);
      }
      if (QuestRewardCountRate != global::MainServer.QuestRewardValueCountRateType.None) {
        output.WriteRawTag(32);
        output.WriteEnum((int) QuestRewardCountRate);
      }
      if (DayWholeNetlocked != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(DayWholeNetlocked);
      }
      if (QuestRewardValue_.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(QuestRewardValue_);
      }
      if (QuestRewardBaseMoney != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(QuestRewardBaseMoney);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QuestRewardType != global::MainServer.QuestRewardType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) QuestRewardType);
      }
      if (QuestRewardRate != 0F) {
        size += 1 + 4;
      }
      if (QuestRewardCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestRewardCount);
      }
      if (QuestRewardCountRate != global::MainServer.QuestRewardValueCountRateType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) QuestRewardCountRate);
      }
      if (DayWholeNetlocked != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(DayWholeNetlocked);
      }
      if (QuestRewardValue_.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(QuestRewardValue_);
      }
      if (QuestRewardBaseMoney != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestRewardBaseMoney);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestRewardValue other) {
      if (other == null) {
        return;
      }
      if (other.QuestRewardType != global::MainServer.QuestRewardType.None) {
        QuestRewardType = other.QuestRewardType;
      }
      if (other.QuestRewardRate != 0F) {
        QuestRewardRate = other.QuestRewardRate;
      }
      if (other.QuestRewardCount != 0) {
        QuestRewardCount = other.QuestRewardCount;
      }
      if (other.QuestRewardCountRate != global::MainServer.QuestRewardValueCountRateType.None) {
        QuestRewardCountRate = other.QuestRewardCountRate;
      }
      if (other.DayWholeNetlocked != 0) {
        DayWholeNetlocked = other.DayWholeNetlocked;
      }
      if (other.QuestRewardValue_.Length != 0) {
        QuestRewardValue_ = other.QuestRewardValue_;
      }
      if (other.QuestRewardBaseMoney != 0) {
        QuestRewardBaseMoney = other.QuestRewardBaseMoney;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestRewardType = (global::MainServer.QuestRewardType) input.ReadEnum();
            break;
          }
          case 21: {
            QuestRewardRate = input.ReadFloat();
            break;
          }
          case 24: {
            QuestRewardCount = input.ReadInt32();
            break;
          }
          case 32: {
            QuestRewardCountRate = (global::MainServer.QuestRewardValueCountRateType) input.ReadEnum();
            break;
          }
          case 40: {
            DayWholeNetlocked = input.ReadInt32();
            break;
          }
          case 50: {
            QuestRewardValue_ = input.ReadString();
            break;
          }
          case 56: {
            QuestRewardBaseMoney = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestRewardType = (global::MainServer.QuestRewardType) input.ReadEnum();
            break;
          }
          case 21: {
            QuestRewardRate = input.ReadFloat();
            break;
          }
          case 24: {
            QuestRewardCount = input.ReadInt32();
            break;
          }
          case 32: {
            QuestRewardCountRate = (global::MainServer.QuestRewardValueCountRateType) input.ReadEnum();
            break;
          }
          case 40: {
            DayWholeNetlocked = input.ReadInt32();
            break;
          }
          case 50: {
            QuestRewardValue_ = input.ReadString();
            break;
          }
          case 56: {
            QuestRewardBaseMoney = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestBroadcastInfo : pb::IMessage<QuestBroadcastInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestBroadcastInfo> _parser = new pb::MessageParser<QuestBroadcastInfo>(() => new QuestBroadcastInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestBroadcastInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestBroadcastInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestBroadcastInfo(QuestBroadcastInfo other) : this() {
      questBroadcastType_ = other.questBroadcastType_;
      questBroadcastValue_ = other.questBroadcastValue_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestBroadcastInfo Clone() {
      return new QuestBroadcastInfo(this);
    }

    /// <summary>Field number for the "quest_broadcast_type" field.</summary>
    public const int QuestBroadcastTypeFieldNumber = 1;
    private global::MainServer.QuestBroadcastType questBroadcastType_ = global::MainServer.QuestBroadcastType.QuestBroadcastNone;
    /// <summary>
    /// 广播类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.QuestBroadcastType QuestBroadcastType {
      get { return questBroadcastType_; }
      set {
        questBroadcastType_ = value;
      }
    }

    /// <summary>Field number for the "quest_broadcast_value" field.</summary>
    public const int QuestBroadcastValueFieldNumber = 2;
    private string questBroadcastValue_ = "";
    /// <summary>
    /// 广播type对应的value (不是广播内容) 比如说什么地区
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string QuestBroadcastValue {
      get { return questBroadcastValue_; }
      set {
        questBroadcastValue_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestBroadcastInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestBroadcastInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestBroadcastType != other.QuestBroadcastType) return false;
      if (QuestBroadcastValue != other.QuestBroadcastValue) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestBroadcastType != global::MainServer.QuestBroadcastType.QuestBroadcastNone) hash ^= QuestBroadcastType.GetHashCode();
      if (QuestBroadcastValue.Length != 0) hash ^= QuestBroadcastValue.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestBroadcastType != global::MainServer.QuestBroadcastType.QuestBroadcastNone) {
        output.WriteRawTag(8);
        output.WriteEnum((int) QuestBroadcastType);
      }
      if (QuestBroadcastValue.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(QuestBroadcastValue);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestBroadcastType != global::MainServer.QuestBroadcastType.QuestBroadcastNone) {
        output.WriteRawTag(8);
        output.WriteEnum((int) QuestBroadcastType);
      }
      if (QuestBroadcastValue.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(QuestBroadcastValue);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QuestBroadcastType != global::MainServer.QuestBroadcastType.QuestBroadcastNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) QuestBroadcastType);
      }
      if (QuestBroadcastValue.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(QuestBroadcastValue);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestBroadcastInfo other) {
      if (other == null) {
        return;
      }
      if (other.QuestBroadcastType != global::MainServer.QuestBroadcastType.QuestBroadcastNone) {
        QuestBroadcastType = other.QuestBroadcastType;
      }
      if (other.QuestBroadcastValue.Length != 0) {
        QuestBroadcastValue = other.QuestBroadcastValue;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestBroadcastType = (global::MainServer.QuestBroadcastType) input.ReadEnum();
            break;
          }
          case 18: {
            QuestBroadcastValue = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestBroadcastType = (global::MainServer.QuestBroadcastType) input.ReadEnum();
            break;
          }
          case 18: {
            QuestBroadcastValue = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QuestTypeValue : pb::IMessage<QuestTypeValue>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestTypeValue> _parser = new pb::MessageParser<QuestTypeValue>(() => new QuestTypeValue());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QuestTypeValue> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.QuestInfoReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestTypeValue() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestTypeValue(QuestTypeValue other) : this() {
      questTypeValueId_ = other.questTypeValueId_;
      questTypeValue_ = other.questTypeValue_;
      isStringValueId_ = other.isStringValueId_;
      isStringValue_ = other.isStringValue_;
      questTypeValueString_ = other.questTypeValueString_;
      questTypeValueStringId_ = other.questTypeValueStringId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QuestTypeValue Clone() {
      return new QuestTypeValue(this);
    }

    /// <summary>Field number for the "quest_type_value_id" field.</summary>
    public const int QuestTypeValueIdFieldNumber = 1;
    private int questTypeValueId_;
    /// <summary>
    /// quest值id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int QuestTypeValueId {
      get { return questTypeValueId_; }
      set {
        questTypeValueId_ = value;
      }
    }

    /// <summary>Field number for the "quest_type_value" field.</summary>
    public const int QuestTypeValue_FieldNumber = 2;
    private long questTypeValue_;
    /// <summary>
    /// quest值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long QuestTypeValue_ {
      get { return questTypeValue_; }
      set {
        questTypeValue_ = value;
      }
    }

    /// <summary>Field number for the "is_string_value_id" field.</summary>
    public const int IsStringValueIdFieldNumber = 3;
    private bool isStringValueId_;
    /// <summary>
    /// 是否为字符串id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsStringValueId {
      get { return isStringValueId_; }
      set {
        isStringValueId_ = value;
      }
    }

    /// <summary>Field number for the "is_string_value" field.</summary>
    public const int IsStringValueFieldNumber = 4;
    private bool isStringValue_;
    /// <summary>
    /// 是否为字符串value
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsStringValue {
      get { return isStringValue_; }
      set {
        isStringValue_ = value;
      }
    }

    /// <summary>Field number for the "quest_type_value_string" field.</summary>
    public const int QuestTypeValueStringFieldNumber = 5;
    private string questTypeValueString_ = "";
    /// <summary>
    /// quest值字符串
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string QuestTypeValueString {
      get { return questTypeValueString_; }
      set {
        questTypeValueString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "quest_type_value_string_id" field.</summary>
    public const int QuestTypeValueStringIdFieldNumber = 6;
    private string questTypeValueStringId_ = "";
    /// <summary>
    /// quest值字符串id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string QuestTypeValueStringId {
      get { return questTypeValueStringId_; }
      set {
        questTypeValueStringId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QuestTypeValue);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QuestTypeValue other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestTypeValueId != other.QuestTypeValueId) return false;
      if (QuestTypeValue_ != other.QuestTypeValue_) return false;
      if (IsStringValueId != other.IsStringValueId) return false;
      if (IsStringValue != other.IsStringValue) return false;
      if (QuestTypeValueString != other.QuestTypeValueString) return false;
      if (QuestTypeValueStringId != other.QuestTypeValueStringId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestTypeValueId != 0) hash ^= QuestTypeValueId.GetHashCode();
      if (QuestTypeValue_ != 0L) hash ^= QuestTypeValue_.GetHashCode();
      if (IsStringValueId != false) hash ^= IsStringValueId.GetHashCode();
      if (IsStringValue != false) hash ^= IsStringValue.GetHashCode();
      if (QuestTypeValueString.Length != 0) hash ^= QuestTypeValueString.GetHashCode();
      if (QuestTypeValueStringId.Length != 0) hash ^= QuestTypeValueStringId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestTypeValueId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestTypeValueId);
      }
      if (QuestTypeValue_ != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(QuestTypeValue_);
      }
      if (IsStringValueId != false) {
        output.WriteRawTag(24);
        output.WriteBool(IsStringValueId);
      }
      if (IsStringValue != false) {
        output.WriteRawTag(32);
        output.WriteBool(IsStringValue);
      }
      if (QuestTypeValueString.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(QuestTypeValueString);
      }
      if (QuestTypeValueStringId.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(QuestTypeValueStringId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestTypeValueId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestTypeValueId);
      }
      if (QuestTypeValue_ != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(QuestTypeValue_);
      }
      if (IsStringValueId != false) {
        output.WriteRawTag(24);
        output.WriteBool(IsStringValueId);
      }
      if (IsStringValue != false) {
        output.WriteRawTag(32);
        output.WriteBool(IsStringValue);
      }
      if (QuestTypeValueString.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(QuestTypeValueString);
      }
      if (QuestTypeValueStringId.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(QuestTypeValueStringId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QuestTypeValueId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestTypeValueId);
      }
      if (QuestTypeValue_ != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(QuestTypeValue_);
      }
      if (IsStringValueId != false) {
        size += 1 + 1;
      }
      if (IsStringValue != false) {
        size += 1 + 1;
      }
      if (QuestTypeValueString.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(QuestTypeValueString);
      }
      if (QuestTypeValueStringId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(QuestTypeValueStringId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QuestTypeValue other) {
      if (other == null) {
        return;
      }
      if (other.QuestTypeValueId != 0) {
        QuestTypeValueId = other.QuestTypeValueId;
      }
      if (other.QuestTypeValue_ != 0L) {
        QuestTypeValue_ = other.QuestTypeValue_;
      }
      if (other.IsStringValueId != false) {
        IsStringValueId = other.IsStringValueId;
      }
      if (other.IsStringValue != false) {
        IsStringValue = other.IsStringValue;
      }
      if (other.QuestTypeValueString.Length != 0) {
        QuestTypeValueString = other.QuestTypeValueString;
      }
      if (other.QuestTypeValueStringId.Length != 0) {
        QuestTypeValueStringId = other.QuestTypeValueStringId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestTypeValueId = input.ReadInt32();
            break;
          }
          case 16: {
            QuestTypeValue_ = input.ReadInt64();
            break;
          }
          case 24: {
            IsStringValueId = input.ReadBool();
            break;
          }
          case 32: {
            IsStringValue = input.ReadBool();
            break;
          }
          case 42: {
            QuestTypeValueString = input.ReadString();
            break;
          }
          case 50: {
            QuestTypeValueStringId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestTypeValueId = input.ReadInt32();
            break;
          }
          case 16: {
            QuestTypeValue_ = input.ReadInt64();
            break;
          }
          case 24: {
            IsStringValueId = input.ReadBool();
            break;
          }
          case 32: {
            IsStringValue = input.ReadBool();
            break;
          }
          case 42: {
            QuestTypeValueString = input.ReadString();
            break;
          }
          case 50: {
            QuestTypeValueStringId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
