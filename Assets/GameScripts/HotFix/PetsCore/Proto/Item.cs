// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: MainServer/Item.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace MainServer {

  /// <summary>Holder for reflection information generated from MainServer/Item.proto</summary>
  public static partial class ItemReflection {

    #region Descriptor
    /// <summary>File descriptor for MainServer/Item.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ItemReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChVNYWluU2VydmVyL0l0ZW0ucHJvdG8SCk1haW5TZXJ2ZXIaFU1haW5TZXJ2",
            "ZXIvUG9rZS5wcm90bxoaTWFpblNlcnZlci9JbnZlbnRvcnkucHJvdG8aHE1h",
            "aW5TZXJ2ZXIvVHJhaW5lclRlYW0ucHJvdG8aGE1haW5TZXJ2ZXIvVHJhaW5l",
            "ci5wcm90byKrAQoESXRlbRIKCgJpZBgBIAEoAxILCgN0aWQYAiABKAMSDAoE",
            "bmFtZRgEIAEoCRINCgVjb3VudBgFIAEoBRINCgVwcmljZRgGIAEoARISCgpz",
            "YWxlX2NvdW50GAcgASgFEiQKBWV4dHJhGAggASgLMhUuTWFpblNlcnZlci5J",
            "dGVtRXh0cmESEQoJY3JlYXRlX3RzGAkgASgDEhEKCXVwZGF0ZV90cxgKIAEo",
            "AyILCglJdGVtRXh0cmEivAEKCkl0ZW1GaWx0ZXISDQoFbmFtZXMYASADKAkS",
            "KAoEc29ydBgCIAEoDjIaLk1haW5TZXJ2ZXIuSXRlbUZpbHRlclNvcnQSDAoE",
            "cGFnZRgDIAEoBRIRCglwYWdlX3NpemUYBCABKAUSEQoJbWluX3ByaWNlGAUg",
            "ASgBEhEKCW1heF9wcmljZRgGIAEoARIRCgl1cGRhdGVfdHMYByABKAMSDAoE",
            "c2FsZRgIIAEoCBINCgVvd25lchgNIAEoCCJICgtVc2VJdGVtSW5mbxIRCglp",
            "dGVtX25hbWUYASABKAkSEAoIcXVhbnRpdHkYAiABKAUSFAoMdGFyZ2V0UG9r",
            "ZUlkGAMgASgDImAKE1VzZUl0ZW1JbmZvUmVzcG9uc2USEQoJaXRlbV9uYW1l",
            "GAEgASgJEhAKCHF1YW50aXR5GAIgASgFEiQKCnRhcmdldFBva2UYAyABKAsy",
            "EC5NYWluU2VydmVyLlBva2UibQoaVXNlVHJhaW5lckl0ZW1JbmZvUmVzcG9u",
            "c2USEQoJaXRlbV9uYW1lGAEgASgJEhAKCHF1YW50aXR5GAIgASgFEioKDXRh",
            "cmdldFRyYWluZXIYAyABKAsyEy5NYWluU2VydmVyLlRyYWluZXIinQEKCUxv",
            "Y2FsSXRlbRIMCgRuYW1lGAEgASgJEgwKBGNvc3QYAiABKAUSJwoEdHlwZRgD",
            "IAEoDjIZLk1haW5TZXJ2ZXIuSW52ZW50b3J5VHlwZRIQCghkdXJhdGlvbhgE",
            "IAEoBRImCgV0ZWFtcxgFIAMoDjIXLk1haW5TZXJ2ZXIuVHJhaW5lclRlYW0S",
            "EQoJdGVhbV9jb3N0GAYgASgFKh0KDkl0ZW1GaWx0ZXJTb3J0EgsKB3ByaWNl",
            "X2kQAEIhWh9nby1uYWthbWEtcG9rZS9wcm90by9NYWluU2VydmVyYgZwcm90",
            "bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::MainServer.PokeReflection.Descriptor, global::MainServer.InventoryReflection.Descriptor, global::MainServer.TrainerTeamReflection.Descriptor, global::MainServer.TrainerReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::MainServer.ItemFilterSort), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.Item), global::MainServer.Item.Parser, new[]{ "Id", "Tid", "Name", "Count", "Price", "SaleCount", "Extra", "CreateTs", "UpdateTs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.ItemExtra), global::MainServer.ItemExtra.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.ItemFilter), global::MainServer.ItemFilter.Parser, new[]{ "Names", "Sort", "Page", "PageSize", "MinPrice", "MaxPrice", "UpdateTs", "Sale", "Owner" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.UseItemInfo), global::MainServer.UseItemInfo.Parser, new[]{ "ItemName", "Quantity", "TargetPokeId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.UseItemInfoResponse), global::MainServer.UseItemInfoResponse.Parser, new[]{ "ItemName", "Quantity", "TargetPoke" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.UseTrainerItemInfoResponse), global::MainServer.UseTrainerItemInfoResponse.Parser, new[]{ "ItemName", "Quantity", "TargetTrainer" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.LocalItem), global::MainServer.LocalItem.Parser, new[]{ "Name", "Cost", "Type", "Duration", "Teams", "TeamCost" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum ItemFilterSort {
    /// <summary>
    ///默认用价格排序
    /// </summary>
    [pbr::OriginalName("price_i")] PriceI = 0,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Item : pb::IMessage<Item>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Item> _parser = new pb::MessageParser<Item>(() => new Item());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Item> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.ItemReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Item() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Item(Item other) : this() {
      id_ = other.id_;
      tid_ = other.tid_;
      name_ = other.name_;
      count_ = other.count_;
      price_ = other.price_;
      saleCount_ = other.saleCount_;
      extra_ = other.extra_ != null ? other.extra_.Clone() : null;
      createTs_ = other.createTs_;
      updateTs_ = other.updateTs_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Item Clone() {
      return new Item(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private long id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "tid" field.</summary>
    public const int TidFieldNumber = 2;
    private long tid_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Tid {
      get { return tid_; }
      set {
        tid_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 4;
    private string name_ = "";
    /// <summary>
    /// int32 item_type = 3;
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int CountFieldNumber = 5;
    private int count_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    /// <summary>Field number for the "price" field.</summary>
    public const int PriceFieldNumber = 6;
    private double price_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double Price {
      get { return price_; }
      set {
        price_ = value;
      }
    }

    /// <summary>Field number for the "sale_count" field.</summary>
    public const int SaleCountFieldNumber = 7;
    private int saleCount_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int SaleCount {
      get { return saleCount_; }
      set {
        saleCount_ = value;
      }
    }

    /// <summary>Field number for the "extra" field.</summary>
    public const int ExtraFieldNumber = 8;
    private global::MainServer.ItemExtra extra_;
    /// <summary>
    /// 额外信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.ItemExtra Extra {
      get { return extra_; }
      set {
        extra_ = value;
      }
    }

    /// <summary>Field number for the "create_ts" field.</summary>
    public const int CreateTsFieldNumber = 9;
    private long createTs_;
    /// <summary>
    /// 创建时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CreateTs {
      get { return createTs_; }
      set {
        createTs_ = value;
      }
    }

    /// <summary>Field number for the "update_ts" field.</summary>
    public const int UpdateTsFieldNumber = 10;
    private long updateTs_;
    /// <summary>
    /// 更新时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long UpdateTs {
      get { return updateTs_; }
      set {
        updateTs_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Item);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Item other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Tid != other.Tid) return false;
      if (Name != other.Name) return false;
      if (Count != other.Count) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(Price, other.Price)) return false;
      if (SaleCount != other.SaleCount) return false;
      if (!object.Equals(Extra, other.Extra)) return false;
      if (CreateTs != other.CreateTs) return false;
      if (UpdateTs != other.UpdateTs) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0L) hash ^= Id.GetHashCode();
      if (Tid != 0L) hash ^= Tid.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (Count != 0) hash ^= Count.GetHashCode();
      if (Price != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(Price);
      if (SaleCount != 0) hash ^= SaleCount.GetHashCode();
      if (extra_ != null) hash ^= Extra.GetHashCode();
      if (CreateTs != 0L) hash ^= CreateTs.GetHashCode();
      if (UpdateTs != 0L) hash ^= UpdateTs.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (Tid != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Tid);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Name);
      }
      if (Count != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(Count);
      }
      if (Price != 0D) {
        output.WriteRawTag(49);
        output.WriteDouble(Price);
      }
      if (SaleCount != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(SaleCount);
      }
      if (extra_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(Extra);
      }
      if (CreateTs != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(CreateTs);
      }
      if (UpdateTs != 0L) {
        output.WriteRawTag(80);
        output.WriteInt64(UpdateTs);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (Tid != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Tid);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Name);
      }
      if (Count != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(Count);
      }
      if (Price != 0D) {
        output.WriteRawTag(49);
        output.WriteDouble(Price);
      }
      if (SaleCount != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(SaleCount);
      }
      if (extra_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(Extra);
      }
      if (CreateTs != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(CreateTs);
      }
      if (UpdateTs != 0L) {
        output.WriteRawTag(80);
        output.WriteInt64(UpdateTs);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Id);
      }
      if (Tid != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Tid);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (Count != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Count);
      }
      if (Price != 0D) {
        size += 1 + 8;
      }
      if (SaleCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SaleCount);
      }
      if (extra_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Extra);
      }
      if (CreateTs != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CreateTs);
      }
      if (UpdateTs != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(UpdateTs);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Item other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0L) {
        Id = other.Id;
      }
      if (other.Tid != 0L) {
        Tid = other.Tid;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.Count != 0) {
        Count = other.Count;
      }
      if (other.Price != 0D) {
        Price = other.Price;
      }
      if (other.SaleCount != 0) {
        SaleCount = other.SaleCount;
      }
      if (other.extra_ != null) {
        if (extra_ == null) {
          Extra = new global::MainServer.ItemExtra();
        }
        Extra.MergeFrom(other.Extra);
      }
      if (other.CreateTs != 0L) {
        CreateTs = other.CreateTs;
      }
      if (other.UpdateTs != 0L) {
        UpdateTs = other.UpdateTs;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 16: {
            Tid = input.ReadInt64();
            break;
          }
          case 34: {
            Name = input.ReadString();
            break;
          }
          case 40: {
            Count = input.ReadInt32();
            break;
          }
          case 49: {
            Price = input.ReadDouble();
            break;
          }
          case 56: {
            SaleCount = input.ReadInt32();
            break;
          }
          case 66: {
            if (extra_ == null) {
              Extra = new global::MainServer.ItemExtra();
            }
            input.ReadMessage(Extra);
            break;
          }
          case 72: {
            CreateTs = input.ReadInt64();
            break;
          }
          case 80: {
            UpdateTs = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 16: {
            Tid = input.ReadInt64();
            break;
          }
          case 34: {
            Name = input.ReadString();
            break;
          }
          case 40: {
            Count = input.ReadInt32();
            break;
          }
          case 49: {
            Price = input.ReadDouble();
            break;
          }
          case 56: {
            SaleCount = input.ReadInt32();
            break;
          }
          case 66: {
            if (extra_ == null) {
              Extra = new global::MainServer.ItemExtra();
            }
            input.ReadMessage(Extra);
            break;
          }
          case 72: {
            CreateTs = input.ReadInt64();
            break;
          }
          case 80: {
            UpdateTs = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ItemExtra : pb::IMessage<ItemExtra>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ItemExtra> _parser = new pb::MessageParser<ItemExtra>(() => new ItemExtra());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ItemExtra> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.ItemReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ItemExtra() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ItemExtra(ItemExtra other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ItemExtra Clone() {
      return new ItemExtra(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ItemExtra);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ItemExtra other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ItemExtra other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 在前端进行名称筛选 再传到后面
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ItemFilter : pb::IMessage<ItemFilter>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ItemFilter> _parser = new pb::MessageParser<ItemFilter>(() => new ItemFilter());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ItemFilter> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.ItemReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ItemFilter() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ItemFilter(ItemFilter other) : this() {
      names_ = other.names_.Clone();
      sort_ = other.sort_;
      page_ = other.page_;
      pageSize_ = other.pageSize_;
      minPrice_ = other.minPrice_;
      maxPrice_ = other.maxPrice_;
      updateTs_ = other.updateTs_;
      sale_ = other.sale_;
      owner_ = other.owner_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ItemFilter Clone() {
      return new ItemFilter(this);
    }

    /// <summary>Field number for the "names" field.</summary>
    public const int NamesFieldNumber = 1;
    private static readonly pb::FieldCodec<string> _repeated_names_codec
        = pb::FieldCodec.ForString(10);
    private readonly pbc::RepeatedField<string> names_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> Names {
      get { return names_; }
    }

    /// <summary>Field number for the "sort" field.</summary>
    public const int SortFieldNumber = 2;
    private global::MainServer.ItemFilterSort sort_ = global::MainServer.ItemFilterSort.PriceI;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.ItemFilterSort Sort {
      get { return sort_; }
      set {
        sort_ = value;
      }
    }

    /// <summary>Field number for the "page" field.</summary>
    public const int PageFieldNumber = 3;
    private int page_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Page {
      get { return page_; }
      set {
        page_ = value;
      }
    }

    /// <summary>Field number for the "page_size" field.</summary>
    public const int PageSizeFieldNumber = 4;
    private int pageSize_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int PageSize {
      get { return pageSize_; }
      set {
        pageSize_ = value;
      }
    }

    /// <summary>Field number for the "min_price" field.</summary>
    public const int MinPriceFieldNumber = 5;
    private double minPrice_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double MinPrice {
      get { return minPrice_; }
      set {
        minPrice_ = value;
      }
    }

    /// <summary>Field number for the "max_price" field.</summary>
    public const int MaxPriceFieldNumber = 6;
    private double maxPrice_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double MaxPrice {
      get { return maxPrice_; }
      set {
        maxPrice_ = value;
      }
    }

    /// <summary>Field number for the "update_ts" field.</summary>
    public const int UpdateTsFieldNumber = 7;
    private long updateTs_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long UpdateTs {
      get { return updateTs_; }
      set {
        updateTs_ = value;
      }
    }

    /// <summary>Field number for the "sale" field.</summary>
    public const int SaleFieldNumber = 8;
    private bool sale_;
    /// <summary>
    ///sale_count > 0
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Sale {
      get { return sale_; }
      set {
        sale_ = value;
      }
    }

    /// <summary>Field number for the "owner" field.</summary>
    public const int OwnerFieldNumber = 13;
    private bool owner_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Owner {
      get { return owner_; }
      set {
        owner_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ItemFilter);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ItemFilter other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!names_.Equals(other.names_)) return false;
      if (Sort != other.Sort) return false;
      if (Page != other.Page) return false;
      if (PageSize != other.PageSize) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(MinPrice, other.MinPrice)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(MaxPrice, other.MaxPrice)) return false;
      if (UpdateTs != other.UpdateTs) return false;
      if (Sale != other.Sale) return false;
      if (Owner != other.Owner) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= names_.GetHashCode();
      if (Sort != global::MainServer.ItemFilterSort.PriceI) hash ^= Sort.GetHashCode();
      if (Page != 0) hash ^= Page.GetHashCode();
      if (PageSize != 0) hash ^= PageSize.GetHashCode();
      if (MinPrice != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(MinPrice);
      if (MaxPrice != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(MaxPrice);
      if (UpdateTs != 0L) hash ^= UpdateTs.GetHashCode();
      if (Sale != false) hash ^= Sale.GetHashCode();
      if (Owner != false) hash ^= Owner.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      names_.WriteTo(output, _repeated_names_codec);
      if (Sort != global::MainServer.ItemFilterSort.PriceI) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Sort);
      }
      if (Page != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Page);
      }
      if (PageSize != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(PageSize);
      }
      if (MinPrice != 0D) {
        output.WriteRawTag(41);
        output.WriteDouble(MinPrice);
      }
      if (MaxPrice != 0D) {
        output.WriteRawTag(49);
        output.WriteDouble(MaxPrice);
      }
      if (UpdateTs != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(UpdateTs);
      }
      if (Sale != false) {
        output.WriteRawTag(64);
        output.WriteBool(Sale);
      }
      if (Owner != false) {
        output.WriteRawTag(104);
        output.WriteBool(Owner);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      names_.WriteTo(ref output, _repeated_names_codec);
      if (Sort != global::MainServer.ItemFilterSort.PriceI) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Sort);
      }
      if (Page != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Page);
      }
      if (PageSize != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(PageSize);
      }
      if (MinPrice != 0D) {
        output.WriteRawTag(41);
        output.WriteDouble(MinPrice);
      }
      if (MaxPrice != 0D) {
        output.WriteRawTag(49);
        output.WriteDouble(MaxPrice);
      }
      if (UpdateTs != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(UpdateTs);
      }
      if (Sale != false) {
        output.WriteRawTag(64);
        output.WriteBool(Sale);
      }
      if (Owner != false) {
        output.WriteRawTag(104);
        output.WriteBool(Owner);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += names_.CalculateSize(_repeated_names_codec);
      if (Sort != global::MainServer.ItemFilterSort.PriceI) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Sort);
      }
      if (Page != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Page);
      }
      if (PageSize != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PageSize);
      }
      if (MinPrice != 0D) {
        size += 1 + 8;
      }
      if (MaxPrice != 0D) {
        size += 1 + 8;
      }
      if (UpdateTs != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(UpdateTs);
      }
      if (Sale != false) {
        size += 1 + 1;
      }
      if (Owner != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ItemFilter other) {
      if (other == null) {
        return;
      }
      names_.Add(other.names_);
      if (other.Sort != global::MainServer.ItemFilterSort.PriceI) {
        Sort = other.Sort;
      }
      if (other.Page != 0) {
        Page = other.Page;
      }
      if (other.PageSize != 0) {
        PageSize = other.PageSize;
      }
      if (other.MinPrice != 0D) {
        MinPrice = other.MinPrice;
      }
      if (other.MaxPrice != 0D) {
        MaxPrice = other.MaxPrice;
      }
      if (other.UpdateTs != 0L) {
        UpdateTs = other.UpdateTs;
      }
      if (other.Sale != false) {
        Sale = other.Sale;
      }
      if (other.Owner != false) {
        Owner = other.Owner;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            names_.AddEntriesFrom(input, _repeated_names_codec);
            break;
          }
          case 16: {
            Sort = (global::MainServer.ItemFilterSort) input.ReadEnum();
            break;
          }
          case 24: {
            Page = input.ReadInt32();
            break;
          }
          case 32: {
            PageSize = input.ReadInt32();
            break;
          }
          case 41: {
            MinPrice = input.ReadDouble();
            break;
          }
          case 49: {
            MaxPrice = input.ReadDouble();
            break;
          }
          case 56: {
            UpdateTs = input.ReadInt64();
            break;
          }
          case 64: {
            Sale = input.ReadBool();
            break;
          }
          case 104: {
            Owner = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            names_.AddEntriesFrom(ref input, _repeated_names_codec);
            break;
          }
          case 16: {
            Sort = (global::MainServer.ItemFilterSort) input.ReadEnum();
            break;
          }
          case 24: {
            Page = input.ReadInt32();
            break;
          }
          case 32: {
            PageSize = input.ReadInt32();
            break;
          }
          case 41: {
            MinPrice = input.ReadDouble();
            break;
          }
          case 49: {
            MaxPrice = input.ReadDouble();
            break;
          }
          case 56: {
            UpdateTs = input.ReadInt64();
            break;
          }
          case 64: {
            Sale = input.ReadBool();
            break;
          }
          case 104: {
            Owner = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UseItemInfo : pb::IMessage<UseItemInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UseItemInfo> _parser = new pb::MessageParser<UseItemInfo>(() => new UseItemInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UseItemInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.ItemReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UseItemInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UseItemInfo(UseItemInfo other) : this() {
      itemName_ = other.itemName_;
      quantity_ = other.quantity_;
      targetPokeId_ = other.targetPokeId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UseItemInfo Clone() {
      return new UseItemInfo(this);
    }

    /// <summary>Field number for the "item_name" field.</summary>
    public const int ItemNameFieldNumber = 1;
    private string itemName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ItemName {
      get { return itemName_; }
      set {
        itemName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "quantity" field.</summary>
    public const int QuantityFieldNumber = 2;
    private int quantity_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Quantity {
      get { return quantity_; }
      set {
        quantity_ = value;
      }
    }

    /// <summary>Field number for the "targetPokeId" field.</summary>
    public const int TargetPokeIdFieldNumber = 3;
    private long targetPokeId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long TargetPokeId {
      get { return targetPokeId_; }
      set {
        targetPokeId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UseItemInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UseItemInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ItemName != other.ItemName) return false;
      if (Quantity != other.Quantity) return false;
      if (TargetPokeId != other.TargetPokeId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ItemName.Length != 0) hash ^= ItemName.GetHashCode();
      if (Quantity != 0) hash ^= Quantity.GetHashCode();
      if (TargetPokeId != 0L) hash ^= TargetPokeId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ItemName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ItemName);
      }
      if (Quantity != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Quantity);
      }
      if (TargetPokeId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(TargetPokeId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ItemName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ItemName);
      }
      if (Quantity != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Quantity);
      }
      if (TargetPokeId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(TargetPokeId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ItemName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ItemName);
      }
      if (Quantity != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Quantity);
      }
      if (TargetPokeId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(TargetPokeId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UseItemInfo other) {
      if (other == null) {
        return;
      }
      if (other.ItemName.Length != 0) {
        ItemName = other.ItemName;
      }
      if (other.Quantity != 0) {
        Quantity = other.Quantity;
      }
      if (other.TargetPokeId != 0L) {
        TargetPokeId = other.TargetPokeId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ItemName = input.ReadString();
            break;
          }
          case 16: {
            Quantity = input.ReadInt32();
            break;
          }
          case 24: {
            TargetPokeId = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ItemName = input.ReadString();
            break;
          }
          case 16: {
            Quantity = input.ReadInt32();
            break;
          }
          case 24: {
            TargetPokeId = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UseItemInfoResponse : pb::IMessage<UseItemInfoResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UseItemInfoResponse> _parser = new pb::MessageParser<UseItemInfoResponse>(() => new UseItemInfoResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UseItemInfoResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.ItemReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UseItemInfoResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UseItemInfoResponse(UseItemInfoResponse other) : this() {
      itemName_ = other.itemName_;
      quantity_ = other.quantity_;
      targetPoke_ = other.targetPoke_ != null ? other.targetPoke_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UseItemInfoResponse Clone() {
      return new UseItemInfoResponse(this);
    }

    /// <summary>Field number for the "item_name" field.</summary>
    public const int ItemNameFieldNumber = 1;
    private string itemName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ItemName {
      get { return itemName_; }
      set {
        itemName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "quantity" field.</summary>
    public const int QuantityFieldNumber = 2;
    private int quantity_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Quantity {
      get { return quantity_; }
      set {
        quantity_ = value;
      }
    }

    /// <summary>Field number for the "targetPoke" field.</summary>
    public const int TargetPokeFieldNumber = 3;
    private global::MainServer.Poke targetPoke_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.Poke TargetPoke {
      get { return targetPoke_; }
      set {
        targetPoke_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UseItemInfoResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UseItemInfoResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ItemName != other.ItemName) return false;
      if (Quantity != other.Quantity) return false;
      if (!object.Equals(TargetPoke, other.TargetPoke)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ItemName.Length != 0) hash ^= ItemName.GetHashCode();
      if (Quantity != 0) hash ^= Quantity.GetHashCode();
      if (targetPoke_ != null) hash ^= TargetPoke.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ItemName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ItemName);
      }
      if (Quantity != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Quantity);
      }
      if (targetPoke_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(TargetPoke);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ItemName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ItemName);
      }
      if (Quantity != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Quantity);
      }
      if (targetPoke_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(TargetPoke);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ItemName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ItemName);
      }
      if (Quantity != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Quantity);
      }
      if (targetPoke_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(TargetPoke);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UseItemInfoResponse other) {
      if (other == null) {
        return;
      }
      if (other.ItemName.Length != 0) {
        ItemName = other.ItemName;
      }
      if (other.Quantity != 0) {
        Quantity = other.Quantity;
      }
      if (other.targetPoke_ != null) {
        if (targetPoke_ == null) {
          TargetPoke = new global::MainServer.Poke();
        }
        TargetPoke.MergeFrom(other.TargetPoke);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ItemName = input.ReadString();
            break;
          }
          case 16: {
            Quantity = input.ReadInt32();
            break;
          }
          case 26: {
            if (targetPoke_ == null) {
              TargetPoke = new global::MainServer.Poke();
            }
            input.ReadMessage(TargetPoke);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ItemName = input.ReadString();
            break;
          }
          case 16: {
            Quantity = input.ReadInt32();
            break;
          }
          case 26: {
            if (targetPoke_ == null) {
              TargetPoke = new global::MainServer.Poke();
            }
            input.ReadMessage(TargetPoke);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UseTrainerItemInfoResponse : pb::IMessage<UseTrainerItemInfoResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UseTrainerItemInfoResponse> _parser = new pb::MessageParser<UseTrainerItemInfoResponse>(() => new UseTrainerItemInfoResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UseTrainerItemInfoResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.ItemReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UseTrainerItemInfoResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UseTrainerItemInfoResponse(UseTrainerItemInfoResponse other) : this() {
      itemName_ = other.itemName_;
      quantity_ = other.quantity_;
      targetTrainer_ = other.targetTrainer_ != null ? other.targetTrainer_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UseTrainerItemInfoResponse Clone() {
      return new UseTrainerItemInfoResponse(this);
    }

    /// <summary>Field number for the "item_name" field.</summary>
    public const int ItemNameFieldNumber = 1;
    private string itemName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ItemName {
      get { return itemName_; }
      set {
        itemName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "quantity" field.</summary>
    public const int QuantityFieldNumber = 2;
    private int quantity_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Quantity {
      get { return quantity_; }
      set {
        quantity_ = value;
      }
    }

    /// <summary>Field number for the "targetTrainer" field.</summary>
    public const int TargetTrainerFieldNumber = 3;
    private global::MainServer.Trainer targetTrainer_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.Trainer TargetTrainer {
      get { return targetTrainer_; }
      set {
        targetTrainer_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UseTrainerItemInfoResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UseTrainerItemInfoResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ItemName != other.ItemName) return false;
      if (Quantity != other.Quantity) return false;
      if (!object.Equals(TargetTrainer, other.TargetTrainer)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ItemName.Length != 0) hash ^= ItemName.GetHashCode();
      if (Quantity != 0) hash ^= Quantity.GetHashCode();
      if (targetTrainer_ != null) hash ^= TargetTrainer.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ItemName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ItemName);
      }
      if (Quantity != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Quantity);
      }
      if (targetTrainer_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(TargetTrainer);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ItemName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ItemName);
      }
      if (Quantity != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Quantity);
      }
      if (targetTrainer_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(TargetTrainer);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ItemName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ItemName);
      }
      if (Quantity != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Quantity);
      }
      if (targetTrainer_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(TargetTrainer);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UseTrainerItemInfoResponse other) {
      if (other == null) {
        return;
      }
      if (other.ItemName.Length != 0) {
        ItemName = other.ItemName;
      }
      if (other.Quantity != 0) {
        Quantity = other.Quantity;
      }
      if (other.targetTrainer_ != null) {
        if (targetTrainer_ == null) {
          TargetTrainer = new global::MainServer.Trainer();
        }
        TargetTrainer.MergeFrom(other.TargetTrainer);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ItemName = input.ReadString();
            break;
          }
          case 16: {
            Quantity = input.ReadInt32();
            break;
          }
          case 26: {
            if (targetTrainer_ == null) {
              TargetTrainer = new global::MainServer.Trainer();
            }
            input.ReadMessage(TargetTrainer);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ItemName = input.ReadString();
            break;
          }
          case 16: {
            Quantity = input.ReadInt32();
            break;
          }
          case 26: {
            if (targetTrainer_ == null) {
              TargetTrainer = new global::MainServer.Trainer();
            }
            input.ReadMessage(TargetTrainer);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class LocalItem : pb::IMessage<LocalItem>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<LocalItem> _parser = new pb::MessageParser<LocalItem>(() => new LocalItem());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<LocalItem> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.ItemReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public LocalItem() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public LocalItem(LocalItem other) : this() {
      name_ = other.name_;
      cost_ = other.cost_;
      type_ = other.type_;
      duration_ = other.duration_;
      teams_ = other.teams_.Clone();
      teamCost_ = other.teamCost_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public LocalItem Clone() {
      return new LocalItem(this);
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 1;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cost" field.</summary>
    public const int CostFieldNumber = 2;
    private int cost_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Cost {
      get { return cost_; }
      set {
        cost_ = value;
      }
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 3;
    private global::MainServer.InventoryType type_ = global::MainServer.InventoryType.InventoryNor;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.InventoryType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "duration" field.</summary>
    public const int DurationFieldNumber = 4;
    private int duration_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Duration {
      get { return duration_; }
      set {
        duration_ = value;
      }
    }

    /// <summary>Field number for the "teams" field.</summary>
    public const int TeamsFieldNumber = 5;
    private static readonly pb::FieldCodec<global::MainServer.TrainerTeam> _repeated_teams_codec
        = pb::FieldCodec.ForEnum(42, x => (int) x, x => (global::MainServer.TrainerTeam) x);
    private readonly pbc::RepeatedField<global::MainServer.TrainerTeam> teams_ = new pbc::RepeatedField<global::MainServer.TrainerTeam>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::MainServer.TrainerTeam> Teams {
      get { return teams_; }
    }

    /// <summary>Field number for the "team_cost" field.</summary>
    public const int TeamCostFieldNumber = 6;
    private int teamCost_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TeamCost {
      get { return teamCost_; }
      set {
        teamCost_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as LocalItem);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(LocalItem other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Name != other.Name) return false;
      if (Cost != other.Cost) return false;
      if (Type != other.Type) return false;
      if (Duration != other.Duration) return false;
      if(!teams_.Equals(other.teams_)) return false;
      if (TeamCost != other.TeamCost) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (Cost != 0) hash ^= Cost.GetHashCode();
      if (Type != global::MainServer.InventoryType.InventoryNor) hash ^= Type.GetHashCode();
      if (Duration != 0) hash ^= Duration.GetHashCode();
      hash ^= teams_.GetHashCode();
      if (TeamCost != 0) hash ^= TeamCost.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Name);
      }
      if (Cost != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Cost);
      }
      if (Type != global::MainServer.InventoryType.InventoryNor) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Type);
      }
      if (Duration != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Duration);
      }
      teams_.WriteTo(output, _repeated_teams_codec);
      if (TeamCost != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(TeamCost);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Name);
      }
      if (Cost != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Cost);
      }
      if (Type != global::MainServer.InventoryType.InventoryNor) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Type);
      }
      if (Duration != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Duration);
      }
      teams_.WriteTo(ref output, _repeated_teams_codec);
      if (TeamCost != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(TeamCost);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (Cost != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Cost);
      }
      if (Type != global::MainServer.InventoryType.InventoryNor) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Duration != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Duration);
      }
      size += teams_.CalculateSize(_repeated_teams_codec);
      if (TeamCost != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TeamCost);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(LocalItem other) {
      if (other == null) {
        return;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.Cost != 0) {
        Cost = other.Cost;
      }
      if (other.Type != global::MainServer.InventoryType.InventoryNor) {
        Type = other.Type;
      }
      if (other.Duration != 0) {
        Duration = other.Duration;
      }
      teams_.Add(other.teams_);
      if (other.TeamCost != 0) {
        TeamCost = other.TeamCost;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Name = input.ReadString();
            break;
          }
          case 16: {
            Cost = input.ReadInt32();
            break;
          }
          case 24: {
            Type = (global::MainServer.InventoryType) input.ReadEnum();
            break;
          }
          case 32: {
            Duration = input.ReadInt32();
            break;
          }
          case 42:
          case 40: {
            teams_.AddEntriesFrom(input, _repeated_teams_codec);
            break;
          }
          case 48: {
            TeamCost = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Name = input.ReadString();
            break;
          }
          case 16: {
            Cost = input.ReadInt32();
            break;
          }
          case 24: {
            Type = (global::MainServer.InventoryType) input.ReadEnum();
            break;
          }
          case 32: {
            Duration = input.ReadInt32();
            break;
          }
          case 42:
          case 40: {
            teams_.AddEntriesFrom(ref input, _repeated_teams_codec);
            break;
          }
          case 48: {
            TeamCost = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
