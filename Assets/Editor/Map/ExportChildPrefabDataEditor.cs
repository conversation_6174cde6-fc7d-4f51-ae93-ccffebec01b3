#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEditor.Experimental.SceneManagement;
using System.Collections.Generic;
using System.IO;
using System;
using Newtonsoft.Json;
using System.Linq;

public static class UnifiedMapExporter
{
    private const int gridSize = 32;
    private const float cellSize = 1f;
    private static readonly string[] waterMaterialKeywords = new[] {
        "pond_on", "pond_edge", "sea_line02", "pond_line", "pond_on_edge", "sea_un", "sea_on", "sea_rock", "sea_line", "sea_rock_m", "river_r", "river"
    };
    private static readonly string[] isOnWaterMaterialKeywords = new[] {
        "sea_un", "sea_on", "river_r", "river", "pond_on"
    };


    [MenuItem("Tools/Map/导出Prefab数据 + 水面数据（合并版本）")]
    public static void ExportAllMapData()
    {
        var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
        GameObject prefabRoot = PrefabStageUtility.GetCurrentPrefabStage()?.prefabContentsRoot;
        if (prefabRoot == null)
        {
            EditorUtility.DisplayDialog("提示", "请在Prefab模式下打开一个Prefab进行导出。", "确定");
            return;
        }

        string folderPath = Application.dataPath + "/AssetRaw/Map/Json";
        if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

        string outputJsonPath = Path.Combine(folderPath, prefabRoot.name + ".json");

        MapData mapdata = new MapData();

        List<string> prefabTypeNames = new() { "HeartGold", "Platinum", "White2" };
        var npcList = new List<MapNPCData>();
        var initPostions = new List<MapInitPostion>();


        // 水域数据
        var waterMapData = new WaterMapData
        {
            gridSize = gridSize,
            cellSize = cellSize,
            blocks = new List<WaterMapBlock>()
        };
        Transform eventObjectsTransform = null;
        foreach (Transform topLevel in prefabRoot.transform)
        {
            if (topLevel.name.EndsWith("EventObjects"))
            {
                eventObjectsTransform = topLevel;
                eventObjectsTransform.gameObject.SetActive(false);
            }
        }
        foreach (Transform topLevel in prefabRoot.transform)
        {
            var exportList = new List<PrefabData>();
            var transmitMapNames = new HashSet<string>();
            var initPostion = topLevel.GetComponent<MapInitPositionComponent>();
            if (initPostion != null)
            {
                initPostions.Add(new MapInitPostion
                {
                    // gen = initPostion.gen,
                    tagName = initPostion.Name,
                    position = initPostion.transform.localPosition,
                    transmitMapInfo = new PrefabTransmitMapInfo
                    {
                        name = initPostion.Name,
                        position = initPostion.transform.localPosition,
                        isPokeCenter = true
                    }
                });
                continue;
            }
            foreach (Transform child in topLevel.transform)
            {
                if (child == topLevel) continue;
                initPostion = child.GetComponent<MapInitPositionComponent>();
                if (initPostion != null)
                {
                    initPostions.Add(new MapInitPostion
                    {
                        // gen = initPostion.gen,
                        // tagName = initPostion.tagName,
                        tagName = initPostion.Name,
                        position = initPostion.transform.localPosition,
                        transmitMapInfo = new PrefabTransmitMapInfo
                        {
                            name = initPostion.Name,
                            position = initPostion.transform.localPosition,
                            isPokeCenter = true
                        }
                    });
                    continue;
                }
                var npcComponent = child.GetComponent<NinMapNPCComponent>();
                if (npcComponent != null)
                {
                    var oprationDatas = new List<MapYarnOprationData>();
                    var yarnTitleList = new List<string>();
                    var yarnStartOprationTypeList = new List<MapYarnOprationType>();
                    var yarnEndOprationTypeList = new List<MapYarnOprationType>();
                    var yarnStartOprationValues = new List<string>();
                    var yarnEndOprationValues = new List<string>();
                    if (npcComponent.yarnStartOprationType != MapYarnOprationType.None)
                    {
                        if (!yarnTitleList.Contains(npcComponent.yarnTitle))
                        {
                            yarnTitleList.Add(npcComponent.yarnTitle);
                        }
                        yarnStartOprationTypeList.Add(npcComponent.yarnStartOprationType);
                        yarnStartOprationValues.Add("");
                    }
                    if (npcComponent.yarnEndOprationType != MapYarnOprationType.None)
                    {
                        if (!yarnTitleList.Contains(npcComponent.yarnTitle))
                        {
                            yarnTitleList.Add(npcComponent.yarnTitle);
                        }
                        yarnEndOprationTypeList.Add(npcComponent.yarnEndOprationType);
                        yarnEndOprationValues.Add("");
                    }
                    yarnTitleList.AddRange(npcComponent.yarnTitleList);
                    yarnStartOprationTypeList.AddRange(npcComponent.yarnStartOprationTypeList);
                    yarnEndOprationTypeList.AddRange(npcComponent.yarnEndOprationTypeList);
                    yarnStartOprationValues.AddRange(npcComponent.yarnStartOprationValues);
                    yarnEndOprationValues.AddRange(npcComponent.yarnEndOprationValues);
                    if (yarnTitleList.Count != yarnStartOprationTypeList.Count ||
                    yarnTitleList.Count != yarnEndOprationTypeList.Count ||
                    yarnTitleList.Count != yarnStartOprationValues.Count ||
                    yarnTitleList.Count != yarnEndOprationValues.Count)
                    {
                        throw new Exception("yarnTitle 数量不对");
                    }
                    for (int i = 0; i < yarnTitleList.Count; i++)
                    {
                        oprationDatas.Add(new MapYarnOprationData
                        {
                            yarnTitle = yarnTitleList[i],
                            yarnStartOprationType = yarnStartOprationTypeList[i],
                            yarnEndOprationType = yarnEndOprationTypeList[i],
                            yarnStartOprationValue = yarnStartOprationValues[i],
                            yarnEndOprationValue = yarnEndOprationValues[i]
                        });
                    }
                    var npcData = new MapNPCData
                    {
                        name = npcComponent.npcConfigName,
                        position = child.localPosition,
                        defaultIsHiden = npcComponent.defaultIsHiden,
                        oprationDatas = oprationDatas
                    };
                    npcList.Add(npcData);
                    continue;
                }
                string prefabName = child.name;
                Vector3 pos = child.localPosition;
                pos.y += (Mathf.Abs(child.parent.localPosition.y) < 0.1f ? 0 : child.parent.localPosition.y);
                Vector3 rotation = child.localRotation.eulerAngles;
                Vector3 scale = child.localScale;

                string prefabAddress = "";
                GameObject prefabInstance = PrefabUtility.GetNearestPrefabInstanceRoot(child.gameObject);
                if (prefabInstance != null)
                {
                    prefabAddress = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(prefabInstance);
                    bool matched = false;
                    foreach (var name in prefabTypeNames)
                    {
                        if (prefabAddress.Contains(name))
                        {
                            string fileName = Path.GetFileNameWithoutExtension(prefabAddress);
                            prefabAddress = $"{name}_{fileName}";
                            matched = true;
                            break;
                        }
                    }
                    if (!matched)
                        prefabAddress = Path.GetFileNameWithoutExtension(prefabAddress);
                }
                Dictionary<string, PrefabMapTileInfo> prefabMapTileInfos = new();
                foreach (var item in child.GetComponentsInChildren<MapTilePrefabComponent>())
                {
                    var parentName = item.gameObject.transform.parent.name;
                    if (item.gameObject.transform.parent == topLevel)
                    {
                        parentName = typeof(MapLoader).Name;
                    }
                    prefabMapTileInfos[parentName + "_" + item.gameObject.name] = new PrefabMapTileInfo
                    {
                        Bgm = item.Bgm,
                        RegionId = item.RegionId,
                        AreaId = item.AreaId,
                        BattleBgm = item.BattleBgm,
                        IsBattle = item.IsBattle,
                        TileType = item.TileType
                    };
                }
                // PrefabMapTileInfo prefabMapTileInfo = null;
                // var mapTileComponent = child.GetComponent<MapTilePrefabComponent>();
                // if (mapTileComponent != null)
                // {
                //     prefabMapTileInfo = new PrefabMapTileInfo
                //     {
                //         Bgm = mapTileComponent.Bgm,
                //         RegionId = mapTileComponent.RegionId,
                //         AreaId = mapTileComponent.AreaId,
                //         BattleBgm = mapTileComponent.BattleBgm,
                //         IsBattle = mapTileComponent.IsBattle,
                //         TileType = mapTileComponent.TileType
                //     };
                // }
                // MapYarnOprationComponent yarnOprationComponent = child.GetComponent<MapYarnOprationComponent>();
                // if (yarnOprationComponent != null)
                PrefabTransmitMapInfo transmitMapInfo = null;
                var transmitComponent = child.GetComponent<TransmitMapComponent>();
                var transmitBoxCollider = child.GetComponent<BoxCollider>();

                if (transmitComponent != null)
                {
                    if (string.IsNullOrEmpty(transmitComponent.Name))
                    {
                        EditorUtility.DisplayDialog("导出错误", $"空的传送点名称: {child.name}", "确定");
                        return;
                    }

                    if (!transmitMapNames.Add(transmitComponent.Name))
                    {
                        EditorUtility.DisplayDialog("导出错误", $"重复的传送点名称: {transmitComponent.Name}", "确定");
                        return;
                    }

                    transmitMapInfo = new PrefabTransmitMapInfo
                    {
                        name = transmitComponent.Name,
                        toMapName = transmitComponent.ToMapName,
                        position = transmitComponent.transform.localPosition,
                        localScale = transmitComponent.transform.localScale,
                        boxColliderSize = transmitComponent.GetComponent<BoxCollider>().size,
                        isHidenEffect = transmitComponent.isHidenEffect,
                        isPokeCenter = transmitComponent.isPointCenter
                    };
                }

                exportList.Add(new PrefabData
                {
                    name = prefabName,
                    position = pos,
                    rotation = rotation,
                    scale = scale,
                    prefab = string.IsNullOrEmpty(prefabAddress) ? prefabName : prefabAddress,
                    transmitMapInfo = transmitMapInfo,
                    mapTileInfos = prefabMapTileInfos
                });

                if (topLevel.name.EndsWith("Main"))
                {
                    string chunkName = child.name;
                    Vector3 localPos = child.localPosition;
                    Vector3 origin = child.transform.position - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
                    // Vector3 origin = WaterMapChecker.WaterWordOrigin(root.transform.position, new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize));
                    List<WaterCell> waterCells = new();
                    for (int x = 0; x < gridSize; x++)
                    {
                        for (int z = 0; z < gridSize; z++)
                        {
                            Vector3 cellCenter = origin + new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize); // 从高处往下射线
                            Debug.Log($"cellCenter: {cellCenter}");
                            // Vector3 cellCenter = WaterMapChecker.WaterWordOrigin(origin,  new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize));
                            bool isWater = false;
                            bool isWaterEdge = false;
                            string materialName = "(null)";
                            string objectName = "(none)";

                            // Ray ray = new Ray(cellCenter, Vector3.down);
                            float maxDistance = 100f;

                            // bool hitFound = false;
                            // RaycastHit closestHit = new RaycastHit();
                            // float closestDistance = float.MaxValue;
                            var fff = 0.3f;
                            // 检查当前中心 + 8 个邻居方向（共 9 个）
                            Vector3[] directions = new Vector3[]
                            {
                                Vector3.zero,                                   // 自身
                                // new Vector3(-fff * cellSize, 0, 0),            // 左
                                // new Vector3(fff * cellSize, 0, 0),             // 右
                                // new Vector3(0, 0, -fff * cellSize),            // 下
                                // new Vector3(0, 0, fff * cellSize),             // 上
                                // new Vector3(-fff * cellSize, 0, -fff * cellSize), // 左下
                                // new Vector3(fff * cellSize, 0, -fff * cellSize),  // 右下
                                // new Vector3(-fff * cellSize, 0, fff * cellSize),  // 左上
                                // new Vector3(fff * cellSize, 0, fff * cellSize),   // 右上
                            };
                            var surfaceY = 0f;
                            foreach (var offset in directions)
                            {
                                if (isWaterEdge && isWater)
                                {
                                    break;
                                }
                                Vector3 probePoint = cellCenter + offset;
                                Ray ray = new Ray(probePoint, Vector3.down);

                                bool hitFound = false;
                                RaycastHit closestHit = new RaycastHit();
                                float closestDistance = float.MaxValue;

                                foreach (var collider in child.GetComponentsInChildren<Collider>(true))
                                {
                                    if (collider.Raycast(ray, out RaycastHit hit, maxDistance))
                                    {
                                        float distance = hit.distance;
                                        if (distance < closestDistance)
                                        {
                                            closestDistance = distance;
                                            closestHit = hit;
                                            hitFound = true;
                                        }
                                    }
                                }

                                if (hitFound)
                                {
                                    objectName = closestHit.collider.name;

                                    var renderer = closestHit.collider.GetComponent<Renderer>();
                                    if (renderer != null && renderer.sharedMaterial != null)
                                    {
                                        materialName = renderer.sharedMaterial.name.ToLowerInvariant();
                                        foreach (var keyword in waterMaterialKeywords)
                                        {
                                            if (materialName.Contains(keyword))
                                            {
                                                if (!isWaterEdge)
                                                {
                                                    bool onWater = false;
                                                    if (isOnWaterMaterialKeywords.Contains(materialName))
                                                    {
                                                        onWater = true;
                                                    }
                                                    // foreach (var keywordd in isOnWaterMaterialKeywords)
                                                    // {
                                                    //     if (materialName.Contains(keywordd))
                                                    //     {
                                                    //         onWater = true;
                                                    //         break;
                                                    //     }
                                                    // }
                                                    if (!onWater)
                                                    {
                                                        isWaterEdge = true;
                                                    }
                                                }
                                                surfaceY = closestHit.point.y;
                                                isWater = true;
                                                break;
                                            }
                                        }
                                    }

                                }

                                // if (isWater)
                                //     break; // 一旦发现是水，就不需要继续检测其他方向了
                            }

                            if (isWater)
                            {
                                var isScene = prefabStage.scene.GetRootGameObjects()[0].gameObject.name == "Prefab Mode in Context";
                                // var isScene = prefabStage.scene.buildIndex != -1;

                                waterCells.Add(new WaterCell
                                {
                                    coord = isScene ? new Vector2Int(x, z) : new Vector2Int(gridSize - x - 1, z), //在prefab上是这样的
                                    // coord = new Vector2Int(x, z), //在scene上是这样的
                                    surfaceY = surfaceY,   // ✅ 用正确的 Hit 信息
                                    isWaterEdge = isWaterEdge
                                });
                            }



                            // 遍历 root 下所有子对象，找到最近的碰撞
                            // foreach (var collider in root.GetComponentsInChildren<Collider>(true))
                            // {
                            //     if (collider.Raycast(ray, out RaycastHit hit, maxDistance))
                            //     {
                            //         float distance = hit.distance;
                            //         if (distance < closestDistance)
                            //         {
                            //             closestDistance = distance;
                            //             closestHit = hit;
                            //             hitFound = true;
                            //         }
                            //     }
                            // }

                            // if (hitFound)
                            // {
                            //     objectName = closestHit.collider.name;

                            //     var renderer = closestHit.collider.GetComponent<Renderer>();
                            //     if (renderer != null && renderer.sharedMaterial != null)
                            //     {
                            //         materialName = renderer.sharedMaterial.name.ToLowerInvariant();
                            //         foreach (var keyword in waterMaterialKeywords)
                            //         {
                            //             if (materialName.Contains(keyword))
                            //             {
                            //                 isWater = true;
                            //                 break;
                            //             }
                            //         }
                            //     }
                            // }
                            // Scene scene = root.scene;

                            // if (ScenePhysics.Raycast(scene, cellCenter, Vector3.down, out RaycastHit hit, 100f))
                            // {
                            //     objectName = hit.collider.name;

                            //     var renderer = hit.collider.GetComponent<Renderer>();
                            //     if (renderer != null && renderer.sharedMaterial != null)
                            //     {
                            //         materialName = renderer.sharedMaterial.name.ToLowerInvariant();
                            //         foreach (var keyword in waterMaterialKeywords)
                            //         {
                            //             if (materialName.Contains(keyword))
                            //             {
                            //                 isWater = true;
                            //                 break;
                            //             }
                            //         }
                            //     }
                            // }

                            // Debug.Log($"[({x},{z})] Hit Object: {objectName}, Material: {materialName}, IsWater: {isWater}");

                        }
                    }
                    if (waterCells.Count > 0)
                    {
                        waterMapData.blocks.Add(new WaterMapBlock
                        {
                            blockName = chunkName,
                            localPosition = localPos,
                            waterCells = waterCells.ToArray()
                        });
                    }



                    // //水数据
                    // string chunkName = child.name;
                    // Vector3 localPos = child.localPosition;
                    // Vector3 chunkOrigin = child.parent.position + child.localPosition - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
                    // // Vector3 chunkOrigin = child.position - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
                    // List<WaterCell> waterCells = new();

                    // for (int x = 0; x < gridSize; x++)
                    // {
                    //     for (int z = 0; z < gridSize; z++)
                    //     {
                    //         Vector3 cellCenter = chunkOrigin + new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize);
                    //         // 坐标偏移，确保 (0,0) 是地图块左上角
                    //         // Vector3 localOffset = new Vector3(
                    //         //     (x + 0.5f - gridSize / 2f) * cellSize,
                    //         //     50f,
                    //         //     (z + 0.5f - gridSize / 2f) * cellSize
                    //         // );

                    //         // // 把本地位置变成世界坐标，适配 Prefab / Scene 模式
                    //         // Vector3 cellCenter = child.transform.TransformPoint(localOffset);


                    //         Ray ray = new Ray(cellCenter, Vector3.down);
                    //         float maxDistance = 100f;

                    //         bool hitFound = false;
                    //         RaycastHit closestHit = new RaycastHit();
                    //         float closestDistance = float.MaxValue;
                    //         var fff = 0.3f;
                    //         // 检查当前中心 + 8 个邻居方向（共 9 个）
                    //         Vector3[] directions = new Vector3[]
                    //         {
                    //             Vector3.zero,                                   // 自身
                    //             new Vector3(-fff * cellSize, 0, 0),            // 左
                    //             new Vector3(fff * cellSize, 0, 0),             // 右
                    //             new Vector3(0, 0, -fff * cellSize),            // 下
                    //             new Vector3(0, 0, fff * cellSize),             // 上
                    //             new Vector3(-fff * cellSize, 0, -fff * cellSize), // 左下
                    //             new Vector3(fff * cellSize, 0, -fff * cellSize),  // 右下
                    //             new Vector3(-fff * cellSize, 0, fff * cellSize),  // 左上
                    //             new Vector3(fff * cellSize, 0, fff * cellSize),   // 右上
                    //         };

                    //         bool isWaterEdge = false;
                    //         bool isWater = false;
                    //         foreach (var offset in directions)
                    //         {
                    //             // 遍历 root 下所有子对象，找到最近的碰撞
                    //             foreach (var collider in child.GetComponentsInChildren<Collider>(true))
                    //             {
                    //                 if (collider.Raycast(ray, out RaycastHit hit, maxDistance))
                    //                 {
                    //                     float distance = hit.distance;
                    //                     if (distance < closestDistance)
                    //                     {
                    //                         closestDistance = distance;
                    //                         closestHit = hit;
                    //                         hitFound = true;
                    //                     }
                    //                 }
                    //             }

                    //             if (hitFound)
                    //             {
                    //                 var renderer = closestHit.collider.GetComponent<Renderer>();
                    //                 if (renderer != null && renderer.sharedMaterial != null)
                    //                 {
                    //                     string materialName = renderer.sharedMaterial.name.ToLowerInvariant();
                    //                     foreach (var keyword in waterMaterialKeywords)
                    //                     {
                    //                         if (materialName.Contains(keyword))
                    //                         {
                    //                             if(!isWaterEdge) {
                    //                                 bool onWater = false;
                    //                                 if(isOnWaterMaterialKeywords.Contains(materialName)) {
                    //                                     onWater = true;
                    //                                 }
                    //                                 // foreach (var keywordd in isOnWaterMaterialKeywords)
                    //                                 // {
                    //                                 //     if (materialName.Contains(keywordd))
                    //                                 //     {
                    //                                 //         onWater = true;
                    //                                 //         break;
                    //                                 //     }
                    //                                 // }
                    //                                 if(!onWater) {
                    //                                     isWaterEdge = true;
                    //                                 }
                    //                             }
                    //                             isWater = true;
                    //                             break;
                    //                         }
                    //                     }
                    //                     // foreach (var keyword in isOnWaterMaterialKeywords)
                    //                     // {
                    //                     //     if (matName.Contains(keyword))
                    //                     //     {
                    //                     //         isWaterEdge = false;
                    //                     //         break;
                    //                     //     }
                    //                     // }
                    //                     // if(!isWaterEdge) {
                    //                     //     bool onWater = false;
                    //                     //     if (isOnWaterMaterialKeywords.Contains(matName))
                    //                     //     {
                    //                     //         onWater = true;
                    //                     //     }
                    //                     //     // foreach (var keywordd in isOnWaterMaterialKeywords)
                    //                     //     // {

                    //                     //     // }
                    //                     //     if(!onWater) {
                    //                     //         isWaterEdge = true;
                    //                     //     }
                    //                     // }
                    //                     // if (waterMaterialKeywords.Contains(matName))
                    //                     // {
                    //                     //     isWater = true;
                    //                     //     break;
                    //                     // }
                    //                     // foreach (var keyword in waterMaterialKeywords)
                    //                     // {
                    //                     //     if (waterMaterialKeywords.Contains(matName))
                    //                     //     {
                    //                     //         var isScene = prefabStage.scene.GetRootGameObjects()[0].gameObject.name == "Prefab Mode in Context";
                    //                     //         // var isScene = prefabStage.scene.buildIndex != -1;

                    //                     //         waterCells.Add(new WaterCell
                    //                     //         {
                    //                     //             coord = isScene ? new Vector2Int(x, z) : new Vector2Int(gridSize-x-1, z), //在prefab上是这样的
                    //                     //             // coord = new Vector2Int(x, z), //在scene上是这样的
                    //                     //             surfaceY = closestHit.point.y,   // ✅ 用正确的 Hit 信息
                    //                     //             isWaterEdge = isWaterEdge
                    //                     //         });
                    //                     //         break;
                    //                     //     }
                    //                     // }
                    //                 }
                    //             }
                    //         }
                    //         if(isWater) {
                    //             var isScene = prefabStage.scene.GetRootGameObjects()[0].gameObject.name == "Prefab Mode in Context";
                    //             // var isScene = prefabStage.scene.buildIndex != -1;

                    //             waterCells.Add(new WaterCell
                    //             {
                    //                 coord = isScene ? new Vector2Int(x, z) : new Vector2Int(gridSize-x-1, z), //在prefab上是这样的
                    //                 // coord = new Vector2Int(x, z), //在scene上是这样的
                    //                 surfaceY = closestHit.point.y,   // ✅ 用正确的 Hit 信息
                    //                 isWaterEdge = isWaterEdge
                    //             });
                    //         }

                    //         // if (Physics.Raycast(cellCenter, Vector3.down, out RaycastHit hit, 100f))
                    //         // {
                    //         //     var renderer = hit.collider.GetComponent<Renderer>();
                    //         //     if (renderer != null && renderer.sharedMaterial != null)
                    //         //     {
                    //         //         string matName = renderer.sharedMaterial.name.ToLowerInvariant();
                    //         //         foreach (var keyword in waterMaterialKeywords)
                    //         //         {
                    //         //             if (matName.Contains(keyword))
                    //         //             {
                    //         //                 waterCells.Add(new WaterCell
                    //         //                 {
                    //         //                     coord = new Vector2Int(x, z),
                    //         //                     surfaceY = hit.point.y
                    //         //                 });
                    //         //                 break;
                    //         //             }
                    //         //         }
                    //         //     }
                    //         // }
                    //     }
                    // }

                    // if (waterCells.Count > 0)
                    // {
                    //     waterMapData.blocks.Add(new WaterMapBlock
                    //     {
                    //         blockName = chunkName,
                    //         localPosition = localPos,
                    //         waterCells = waterCells.ToArray()
                    //     });
                    // }
                }

            }

            if (exportList.Count > 0)
            {

                //     configFileNames.Add($"Map_{mapName}");
                // configFileNames.Add($"Map_{mapName}Building");
                // configFileNames.Add($"Map_{mapName}Transfer");
                // configFileNames.Add($"Map_{mapName}Env");
                // configFileNames.Add($"Map_{mapName}EnvOutBuilding");
                // configFileNames.Add($"Map_{mapName}MultipleOut");
                // configFileNames.Add($"Map_{mapName}Interior");
                if (topLevel.name.EndsWith("Main"))
                {
                    mapdata.mainMap = exportList;
                }
                else if (topLevel.name.EndsWith("MainBuilding"))
                {
                    mapdata.buildingMap = exportList;
                }
                else if (topLevel.name.EndsWith("Transfer"))
                {
                    mapdata.transferMap = exportList;
                }
                else if (topLevel.name.EndsWith("Env"))
                {
                    mapdata.envMap = exportList;
                }
                else if (topLevel.name.EndsWith("EnvOutBuilding"))
                {
                    mapdata.envOutBuildingMap = exportList;
                }
                else if (topLevel.name.EndsWith("MultipleOut"))
                {
                    mapdata.multipleOutMap = exportList;
                }
                else if (topLevel.name.EndsWith("Interior"))
                {
                    mapdata.interiorMap = exportList;
                }
                else if (topLevel.name.EndsWith("Other"))
                {
                    mapdata.otherMap = exportList;
                }
                else if (topLevel.name.EndsWith("Npc"))
                {
                    //
                }
                else if (topLevel.name.EndsWith("EventObjects"))
                {
                    //
                }
                else
                {
                    EditorUtility.DisplayDialog("导出错误", $"未知的顶层名称: {topLevel.name}", "确定");
                    return;
                }
                // combinedData.prefabData[topLevel.name] = exportList;
            }
        }
        mapdata.npcs = npcList;
        mapdata.initPostions = initPostions;

        // foreach (Transform child in prefabRoot.transform)
        // {
        //     string chunkName = child.name;
        //     Vector3 localPos = child.localPosition;

        //     Vector3 chunkOrigin = child.position - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
        //     List<WaterCell> waterCells = new();

        //     for (int x = 0; x < gridSize; x++)
        //     {
        //         for (int z = 0; z < gridSize; z++)
        //         {
        //             Vector3 cellCenter = chunkOrigin + new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize);

        //             if (Physics.Raycast(cellCenter, Vector3.down, out RaycastHit hit, 100f))
        //             {
        //                 var renderer = hit.collider.GetComponent<Renderer>();
        //                 if (renderer != null && renderer.sharedMaterial != null)
        //                 {
        //                     string matName = renderer.sharedMaterial.name.ToLowerInvariant();
        //                     foreach (var keyword in waterMaterialKeywords)
        //                     {
        //                         if (matName.Contains(keyword))
        //                         {
        //                             waterCells.Add(new WaterCell
        //                             {
        //                                 coord = new Vector2Int(x, z),
        //                                 surfaceY = hit.point.y
        //                             });
        //                             break;
        //                         }
        //                     }
        //                 }
        //             }
        //         }
        //     }

        //     if (waterCells.Count > 0)
        //     {
        //         waterMapData.blocks.Add(new WaterMapBlock
        //         {
        //             blockName = chunkName,
        //             localPosition = localPos,
        //             waterCells = waterCells.ToArray()
        //         });
        //     }
        // }

        mapdata.waterMap = waterMapData;
        var obstacleList = new List<MapRuntimeObstacleData>();
        var eventComponentList = new List<MapTriggerEventComponentInfo>();
        if (eventObjectsTransform != null)
        {
            eventObjectsTransform.gameObject.SetActive(true);
            foreach (Transform child in eventObjectsTransform)
            {
                if (child == eventObjectsTransform) continue;
                string prefabAddress = "";
                GameObject prefabInstance = PrefabUtility.GetNearestPrefabInstanceRoot(child.gameObject);
                if (prefabInstance != null)
                {
                    prefabAddress = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(prefabInstance);
                    bool matched = false;
                    foreach (var name in prefabTypeNames)
                    {
                        if (prefabAddress.Contains(name))
                        {
                            string fileName = Path.GetFileNameWithoutExtension(prefabAddress);
                            prefabAddress = $"{name}_{fileName}";
                            matched = true;
                            break;
                        }
                    }
                    if (!matched)
                        prefabAddress = Path.GetFileNameWithoutExtension(prefabAddress);
                }
                MapTriggerEventComponentInfo eventComponentInfo = null;
                var eventComponent = child.GetComponent<MapTriggerEventComponent>();
                var eventComponentCollider = child.GetComponent<BoxCollider>();

                if (eventComponent != null)
                {
                    if (string.IsNullOrEmpty(eventComponent.eventName) || eventComponent.triggerEventType == MapTriggerEventType.None)
                    {
                        EditorUtility.DisplayDialog("导出错误", $"空的事件点名称: {child.name}", "确定");
                        return;
                    }
                    if (eventComponentCollider == null)
                    {
                        EditorUtility.DisplayDialog("导出错误", $"事件点没有BoxCollider: {child.name}", "确定");
                        return;
                    }
                    eventComponentInfo = new MapTriggerEventComponentInfo
                    {
                        eventName = eventComponent.eventName,
                        eventValue = eventComponent.eventValue,
                        prefab = string.IsNullOrEmpty(prefabAddress) ? child.name : prefabAddress,
                        triggerEventType = eventComponent.triggerEventType,
                        position = eventComponent.transform.localPosition,
                        localScale = eventComponent.transform.localScale,
                        boxColliderSize = eventComponentCollider.size
                    };
                    var oprationDatas = new List<MapYarnOprationData>();
                    var yarnTitleList = new List<string>();
                    var yarnStartOprationTypeList = new List<MapYarnOprationType>();
                    var yarnEndOprationTypeList = new List<MapYarnOprationType>();
                    var yarnStartOprationValues = new List<string>();
                    var yarnEndOprationValues = new List<string>();
                    if (eventComponent.yarnStartOprationType != MapYarnOprationType.None)
                    {
                        if (!yarnTitleList.Contains(eventComponent.yarnTitle))
                        {
                            yarnTitleList.Add(eventComponent.yarnTitle);
                        }
                        yarnStartOprationTypeList.Add(eventComponent.yarnStartOprationType);
                        yarnStartOprationValues.Add("");
                    }
                    if (eventComponent.yarnEndOprationType != MapYarnOprationType.None)
                    {
                        if (!yarnTitleList.Contains(eventComponent.yarnTitle))
                        {
                            yarnTitleList.Add(eventComponent.yarnTitle);
                        }
                        yarnEndOprationTypeList.Add(eventComponent.yarnEndOprationType);
                        yarnEndOprationValues.Add("");
                    }
                    yarnTitleList.AddRange(eventComponent.yarnTitleList);
                    yarnStartOprationTypeList.AddRange(eventComponent.yarnStartOprationTypeList);
                    yarnEndOprationTypeList.AddRange(eventComponent.yarnEndOprationTypeList);
                    yarnStartOprationValues.AddRange(eventComponent.yarnStartOprationValues);
                    yarnEndOprationValues.AddRange(eventComponent.yarnEndOprationValues);
                    if (yarnTitleList.Count != yarnStartOprationTypeList.Count ||
                    yarnTitleList.Count != yarnEndOprationTypeList.Count ||
                    yarnTitleList.Count != yarnStartOprationValues.Count ||
                    yarnTitleList.Count != yarnEndOprationValues.Count)
                    {
                        throw new Exception("yarnTitle 数量不对");
                    }
                    for (int i = 0; i < yarnTitleList.Count; i++)
                    {
                        oprationDatas.Add(new MapYarnOprationData
                        {
                            yarnTitle = yarnTitleList[i],
                            yarnStartOprationType = yarnStartOprationTypeList[i],
                            yarnEndOprationType = yarnEndOprationTypeList[i],
                            yarnStartOprationValue = yarnStartOprationValues[i],
                            yarnEndOprationValue = yarnEndOprationValues[i]
                        });
                    }
                    eventComponentInfo.oprationDatas = oprationDatas;
                    eventComponentList.Add(eventComponentInfo);
                    continue;
                }

                MapRuntimeObstacleData obstacleComponentInfo = null;
                var obstacleComponent = child.GetComponent<MapRuntimeObstacle>();
                var obstacleComponentCollider = child.GetComponent<BoxCollider>();

                // var obstacleComponent = child.GetComponent<NinMapNPCComponent>();
                if (obstacleComponent != null)
                {
                    var oprationDatas = new List<MapYarnOprationData>();
                    var yarnTitleList = new List<string>();
                    var yarnStartOprationTypeList = new List<MapYarnOprationType>();
                    var yarnEndOprationTypeList = new List<MapYarnOprationType>();
                    var yarnStartOprationValues = new List<string>();
                    var yarnEndOprationValues = new List<string>();
                    if (obstacleComponent.yarnStartOprationType != MapYarnOprationType.None)
                    {
                        if (!yarnTitleList.Contains(obstacleComponent.yarnTitle))
                        {
                            yarnTitleList.Add(obstacleComponent.yarnTitle);
                        }
                        yarnStartOprationTypeList.Add(obstacleComponent.yarnStartOprationType);
                        yarnStartOprationValues.Add("");
                    }
                    if (obstacleComponent.yarnEndOprationType != MapYarnOprationType.None)
                    {
                        if (!yarnTitleList.Contains(obstacleComponent.yarnTitle))
                        {
                            yarnTitleList.Add(obstacleComponent.yarnTitle);
                        }
                        yarnEndOprationTypeList.Add(obstacleComponent.yarnEndOprationType);
                        yarnEndOprationValues.Add("");
                    }
                    yarnTitleList.AddRange(obstacleComponent.yarnTitleList);
                    yarnStartOprationTypeList.AddRange(obstacleComponent.yarnStartOprationTypeList);
                    yarnEndOprationTypeList.AddRange(obstacleComponent.yarnEndOprationTypeList);
                    yarnStartOprationValues.AddRange(obstacleComponent.yarnStartOprationValues);
                    yarnEndOprationValues.AddRange(obstacleComponent.yarnEndOprationValues);
                    if (yarnTitleList.Count != yarnStartOprationTypeList.Count ||
                    yarnTitleList.Count != yarnEndOprationTypeList.Count ||
                    yarnTitleList.Count != yarnStartOprationValues.Count ||
                    yarnTitleList.Count != yarnEndOprationValues.Count)
                    {
                        throw new Exception("yarnTitle 数量不对");
                    }
                    for (int i = 0; i < yarnTitleList.Count; i++)
                    {
                        oprationDatas.Add(new MapYarnOprationData
                        {
                            yarnTitle = yarnTitleList[i],
                            yarnStartOprationType = yarnStartOprationTypeList[i],
                            yarnEndOprationType = yarnEndOprationTypeList[i],
                            yarnStartOprationValue = yarnStartOprationValues[i],
                            yarnEndOprationValue = yarnEndOprationValues[i]
                        });
                    }
                    obstacleComponentInfo = new MapRuntimeObstacleData
                    {
                        name = obstacleComponent.name,
                        position = obstacleComponent.transform.localPosition,
                        scale = obstacleComponent.transform.localScale,
                        defaultIsHiden = obstacleComponent.defaultIsHiden,
                        oprationDatas = oprationDatas,
                        prefab = string.IsNullOrEmpty(prefabAddress) ? child.name : prefabAddress,
                    };
                    obstacleList.Add(obstacleComponentInfo);
                    continue;
                }
            }
            mapdata.runtimeObstacles = obstacleList;
            mapdata.triggerEventComponents = eventComponentList;
        }


        var settings = new JsonSerializerSettings
        {
            Converters = new[] {
                new Vec3Conv(),
                //new StringEnumConverter(),
            },
        };
        // string json = JsonUtility.ToJson(mapdata, true);
        // File.WriteAllText(outputJsonPath, json);
        // AssetDatabase.Refresh();

        // EditorUtility.DisplayDialog("✅ 导出完成", $"已保存至:\n{outputJsonPath}", "好");
        // var myObjectINeedToSerialize = new Vector3(1, 2, 3);

        string json = JsonConvert.SerializeObject(mapdata, settings);
        File.WriteAllText(outputJsonPath, json);
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("✅ 导出完成", $"已保存至:\n{outputJsonPath}", "好");
    }


}
#endif
