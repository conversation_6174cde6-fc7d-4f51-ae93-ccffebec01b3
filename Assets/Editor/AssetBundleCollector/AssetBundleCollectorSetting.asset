%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 185f6993d5150494d98da50e26cb1c25, type: 3}
  m_Name: AssetBundleCollectorSetting
  m_EditorClassIdentifier: 
  ShowPackageView: 1
  ShowEditorAlias: 0
  UniqueBundleName: 1
  Packages:
  - PackageName: DefaultPackage
    PackageDesc: 
    EnableAddressable: 1
    LocationToLower: 0
    IncludeAssetGUID: 0
    AutoCollectShaders: 1
    IgnoreRuleName: NormalIgnoreRule
    Groups:
    - GroupName: Actor
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Actor
        CollectorGUID: 9c67ce0c9edfd4e4aa1006ee39846d66
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Audios
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Audios
        CollectorGUID: 8ef7a411b66e70f4cb65c2233c9a0868
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Configs
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Configs
        CollectorGUID: dd2928019aef34248af368b99bc53bea
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: DLL
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/DLL
        CollectorGUID: 3aad79ec1ea08c24c891bd3c669d4125
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Effects
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Effects
        CollectorGUID: 0fe175e1e1bd49a4ca71e66b6a9b7237
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/AssetRaw/Texture/Effect
        CollectorGUID: edae5ee44c518446eb009da7c8ca04bf
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Fonts
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Fonts
        CollectorGUID: 2473375c9ee163a4b861278b38091455
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Materials
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Materials
        CollectorGUID: 228b1547e7065d546ad0bf215fd6a276
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Scenes
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Scenes
        CollectorGUID: cace6ee6539f661419b5e5f8ae1c0146
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: UI
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/UI
        CollectorGUID: 27e87d83814156648b58f380b834e046
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: UIRaw
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/UIRaw/Atlas
        CollectorGUID: 6d11fa91acc253840a648b58f23db139
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/AssetRaw/UIRaw/Raw
        CollectorGUID: 6bc134b912ac6bb4399ea1bec4c11636
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Pokemon
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Texture/Pokemon
        CollectorGUID: c7898b3129746420383c9d7fc7666a81
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/AssetRaw/PokeData/json
        CollectorGUID: 36c467c451add49b28ad209ecb188977
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Trainer
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Texture/Trainer
        CollectorGUID: f4a64ebe5d92748528861730a41da3ae
        CollectorType: 0
        AddressRuleName: AddressByGroupAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Item
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Texture/Items
        CollectorGUID: 2c325d65f0b884e858b12393171036fb
        CollectorType: 0
        AddressRuleName: AddressByGroupAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/AssetRaw/Texture/Pokeballs
        CollectorGUID: 9fc7685294c104fc38313a58e760e032
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Cozy
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Plugins/CozyWeather
        CollectorGUID: f4f05eb531384414ca8d2e3bdb71f705
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Prefabs
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Prefabs/Weather
        CollectorGUID: 5cbd3e56e5a984c50b080a5921f1c550
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/AssetRaw/Prefabs/NinTransitionsPlus.prefab
        CollectorGUID: 38a5ff874fd0f4eb3b0b01ab0ff5b321
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Pokemon_DB
      GroupDesc: 
      AssetTags: Pokemon_DB
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/PokeData/DBRaw
        CollectorGUID: 2dadfa8dcb2484730a6a05a71d6ba938
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Map
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Map/Json
        CollectorGUID: 36dda1c468374439ebc7df8d88cdb2e7
        CollectorType: 0
        AddressRuleName: AddressByGroupAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/AssetRaw/Map/Controllers
        CollectorGUID: 9ec80725f11334bb98edd11812e52a8b
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/AssetRaw/Map/Prefabs/EventObjects
        CollectorGUID: ddd2a5329f3c8438d86cff6e51d9341e
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: HeartGold
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Map/Prefabs/HeartGold
        CollectorGUID: f2cd25ff20f944b36bce01daa20056be
        CollectorType: 0
        AddressRuleName: AddressByGroupAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Platinum
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Map/Prefabs/Platinum
        CollectorGUID: 72768c57ae7cc4d289970a605fcc8881
        CollectorType: 0
        AddressRuleName: AddressByGroupAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: White2
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/Map/Prefabs/White2
        CollectorGUID: c3f0e13d968d8417cae5751c4aa4a450
        CollectorType: 0
        AddressRuleName: AddressByGroupAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Yarn
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/YarnSpinner
        CollectorGUID: b4aea6a88885a451d89a7771e5ee7e59
        CollectorType: 0
        AddressRuleName: AddressByGroupAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
  - PackageName: DefaultPokeDataPackage
    PackageDesc: 
    EnableAddressable: 1
    LocationToLower: 0
    IncludeAssetGUID: 0
    AutoCollectShaders: 1
    IgnoreRuleName: RawFileIgnoreRule
    Groups:
    - GroupName: PokeData
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/AssetRaw/PokeData/raws.zip
        CollectorGUID: a405fd246731c407eae2f759d150139d
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
  - PackageName: OtherPackage
    PackageDesc: 
    EnableAddressable: 1
    LocationToLower: 0
    IncludeAssetGUID: 0
    AutoCollectShaders: 1
    IgnoreRuleName: NormalIgnoreRule
    Groups:
    - GroupName: ModelGroup
      GroupDesc: 
      AssetTags: models
      ActiveRuleName: EnableGroup
      Collectors: []
    - GroupName: SceneGroup
      GroupDesc: 
      AssetTags: scenes
      ActiveRuleName: EnableGroup
      Collectors: []
  - PackageName: Dlc1Package
    PackageDesc: 
    EnableAddressable: 1
    LocationToLower: 0
    IncludeAssetGUID: 0
    AutoCollectShaders: 1
    IgnoreRuleName: NormalIgnoreRule
    Groups:
    - GroupName: ModelGroup
      GroupDesc: 
      AssetTags: models
      ActiveRuleName: EnableGroup
      Collectors: []
    - GroupName: SceneGroup
      GroupDesc: 
      AssetTags: scenes
      ActiveRuleName: EnableGroup
      Collectors: []
  - PackageName: Dlc2Package
    PackageDesc: 
    EnableAddressable: 1
    LocationToLower: 0
    IncludeAssetGUID: 0
    AutoCollectShaders: 1
    IgnoreRuleName: NormalIgnoreRule
    Groups:
    - GroupName: ModelGroup
      GroupDesc: 
      AssetTags: models
      ActiveRuleName: EnableGroup
      Collectors: []
  - PackageName: DefaultPackageEmpty
    PackageDesc: 
    EnableAddressable: 0
    LocationToLower: 0
    IncludeAssetGUID: 0
    AutoCollectShaders: 1
    IgnoreRuleName: NormalIgnoreRule
    Groups:
    - GroupName: Empty
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Resources/transparent_sprite.png
        CollectorGUID: cd4297281716d48fab2e6fa3135da69e
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
